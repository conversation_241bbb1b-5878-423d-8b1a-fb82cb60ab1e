#!/usr/bin/env python3
"""
调试路由问题

测试Flask应用的路由是否正常工作
"""

import os
import sys
from datetime import datetime

def test_routes():
    """测试路由"""
    print("调试Flask路由问题")
    print("=" * 40)
    
    try:
        # 切换到正确的目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # 添加当前目录到Python路径
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        # 导入Flask应用
        from app import create_app
        
        # 创建应用
        app = create_app()
        print("✓ Flask应用创建成功")
        
        # 列出所有路由
        print("\n注册的路由:")
        for rule in app.url_map.iter_rules():
            methods = ', '.join(rule.methods - {'HEAD', 'OPTIONS'})
            print(f"  {methods:15} {rule.rule}")
        
        # 测试应用
        with app.test_client() as client:
            print("\n测试路由:")
            
            # 测试主页
            try:
                response = client.get('/')
                print(f"  GET / : {response.status_code}")
                if response.status_code != 200:
                    print(f"    错误内容: {response.data.decode('utf-8')[:200]}")
            except Exception as e:
                print(f"  GET / : 异常 - {str(e)}")
            
            # 测试testplan页面
            try:
                response = client.get('/testplan')
                print(f"  GET /testplan : {response.status_code}")
                if response.status_code == 200:
                    print("    ✓ testplan页面正常")
                else:
                    print(f"    ❌ 错误内容: {response.data.decode('utf-8')[:200]}")
            except Exception as e:
                print(f"  GET /testplan : 异常 - {str(e)}")
            
            # 测试健康检查
            try:
                response = client.get('/health')
                print(f"  GET /health : {response.status_code}")
                if response.status_code == 200:
                    data = response.get_json()
                    print(f"    状态: {data.get('status', 'unknown')}")
                else:
                    print(f"    错误内容: {response.data.decode('utf-8')[:200]}")
            except Exception as e:
                print(f"  GET /health : 异常 - {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_minimal_app():
    """创建最小化的Flask应用进行测试"""
    print("\n创建最小化Flask应用")
    print("=" * 40)
    
    try:
        from flask import Flask, render_template, jsonify
        
        app = Flask(__name__)
        
        @app.route('/')
        def index():
            return "主页正常工作"
        
        @app.route('/testplan')
        def testplan():
            return "用例管理页面正常工作"
        
        @app.route('/test')
        def test():
            return jsonify({'status': 'ok', 'message': '测试路由正常'})
        
        print("✓ 最小化Flask应用创建成功")
        
        # 测试路由
        with app.test_client() as client:
            print("\n测试最小化应用路由:")
            
            response = client.get('/')
            print(f"  GET / : {response.status_code} - {response.data.decode('utf-8')}")
            
            response = client.get('/testplan')
            print(f"  GET /testplan : {response.status_code} - {response.data.decode('utf-8')}")
            
            response = client.get('/test')
            print(f"  GET /test : {response.status_code} - {response.get_json()}")
        
        print("\n启动最小化测试服务器...")
        print("访问 http://127.0.0.1:5001/testplan 进行测试")
        print("按 Ctrl+C 停止服务器")
        
        try:
            app.run(host='127.0.0.1', port=5001, debug=True)
        except KeyboardInterrupt:
            print("\n测试服务器已停止")
        
        return True
        
    except Exception as e:
        print(f"✗ 最小化应用测试失败: {str(e)}")
        return False

def check_template_issues():
    """检查模板问题"""
    print("\n检查模板问题")
    print("=" * 40)
    
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        templates_dir = os.path.join(script_dir, 'templates')
        
        # 检查testplan.html
        testplan_path = os.path.join(templates_dir, 'testplan.html')
        if not os.path.exists(testplan_path):
            print("❌ testplan.html 不存在")
            return False
        
        # 读取模板内容
        with open(testplan_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✓ testplan.html 存在 ({len(content)} 字符)")
        
        # 检查模板语法
        if '{% extends "base.html" %}' not in content:
            print("⚠️ testplan.html 可能缺少基础模板继承")
        
        if '{% block content %}' not in content:
            print("⚠️ testplan.html 可能缺少内容块")
        
        # 检查base.html
        base_path = os.path.join(templates_dir, 'base.html')
        if not os.path.exists(base_path):
            print("❌ base.html 不存在")
            return False
        
        with open(base_path, 'r', encoding='utf-8') as f:
            base_content = f.read()
        
        print(f"✓ base.html 存在 ({len(base_content)} 字符)")
        
        # 尝试手动渲染模板
        os.chdir(script_dir)
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        from flask import Flask
        app = Flask(__name__)
        
        with app.app_context():
            try:
                from flask import render_template
                result = render_template('testplan.html')
                print(f"✓ testplan.html 模板渲染成功 ({len(result)} 字符)")
                return True
            except Exception as e:
                print(f"❌ testplan.html 模板渲染失败: {str(e)}")
                return False
        
    except Exception as e:
        print(f"✗ 模板检查失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("Flask路由调试工具")
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查是否在正确的目录
    if not os.path.exists('app.py'):
        print("❌ 错误: 请在dashboard_web目录下运行此脚本")
        return False
    
    print("\n选择调试方式:")
    print("1. 测试完整Flask应用")
    print("2. 检查模板问题")
    print("3. 创建最小化测试应用")
    
    try:
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == '1':
            return test_routes()
        elif choice == '2':
            return check_template_issues()
        elif choice == '3':
            return create_minimal_app()
        else:
            print("无效选择，执行完整测试")
            success1 = check_template_issues()
            success2 = test_routes()
            return success1 and success2
            
    except KeyboardInterrupt:
        print("\n调试被用户中断")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n调试被用户中断")
        sys.exit(0)
