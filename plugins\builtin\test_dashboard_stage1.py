#!/usr/bin/env python3
"""
仪表板第一阶段测试脚本

该脚本用于测试第一阶段（基础框架搭建）的完成情况。
"""

import os
import sys
import time
import threading
import webbrowser
from datetime import datetime

def test_plugin_loading():
    """测试插件加载"""
    print("=" * 60)
    print("测试插件加载")
    print("=" * 60)
    
    try:
        # 添加插件路径
        plugin_path = os.path.dirname(os.path.abspath(__file__))
        if plugin_path not in sys.path:
            sys.path.insert(0, plugin_path)
        
        # 导入插件
        from dashboard_plugin import DashboardPlugin
        
        # 创建插件实例
        plugin = DashboardPlugin()
        
        print(f"✓ 插件导入成功")
        print(f"✓ 插件名称: {plugin.name}")
        print(f"✓ 插件版本: {plugin.version}")
        print(f"✓ 插件描述: {plugin.description}")
        
        return plugin
        
    except Exception as e:
        print(f"✗ 插件加载失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_web_server(plugin):
    """测试Web服务器"""
    print("\n" + "=" * 60)
    print("测试Web服务器")
    print("=" * 60)
    
    try:
        # 模拟主窗口对象
        class MockMainWindow:
            def __init__(self):
                self.tools_menu = MockMenu()
        
        class MockMenu:
            def addSeparator(self):
                pass
            def addAction(self, action):
                print(f"✓ 菜单项已添加: {action.text()}")
        
        class MockAction:
            def __init__(self, text, parent):
                self._text = text
                self._parent = parent
                self._status_tip = ""
                self._triggered_callback = None
            
            def text(self):
                return self._text
            
            def setStatusTip(self, tip):
                self._status_tip = tip
            
            def triggered(self):
                return MockSignal()
        
        class MockSignal:
            def connect(self, callback):
                pass
        
        # 替换QAction
        import sys
        if 'PyQt5.QtWidgets' not in sys.modules:
            class MockQAction:
                def __init__(self, text, parent):
                    self._text = text
                    self._parent = parent
                    self._status_tip = ""
                
                def text(self):
                    return self._text
                
                def setStatusTip(self, tip):
                    self._status_tip = tip
                
                def triggered(self):
                    return MockSignal()
            
            # 创建模拟模块
            import types
            mock_qtwidgets = types.ModuleType('PyQt5.QtWidgets')
            mock_qtwidgets.QAction = MockQAction
            mock_qtwidgets.QMessageBox = type('QMessageBox', (), {
                'warning': lambda *args: print(f"警告消息: {args[-1]}")
            })
            
            mock_qtcore = types.ModuleType('PyQt5.QtCore')
            mock_qtcore.QTimer = type('QTimer', (), {
                '__init__': lambda self: None,
                'timeout': MockSignal(),
                'start': lambda self, interval: None,
                'stop': lambda self: None,
                'isActive': lambda self: False
            })
            
            sys.modules['PyQt5.QtWidgets'] = mock_qtwidgets
            sys.modules['PyQt5.QtCore'] = mock_qtcore
        
        # 初始化插件
        main_window = MockMainWindow()
        plugin.initialize(main_window)
        
        # 等待服务器启动
        print("等待Web服务器启动...")
        time.sleep(3)
        
        # 检查服务器状态
        server_info = plugin.get_server_info()
        print(f"✓ 服务器状态: {server_info}")
        
        if server_info['running']:
            print(f"✓ Web服务器启动成功，端口: {server_info['port']}")
            print(f"✓ 访问地址: {server_info['url']}")
            return True
        else:
            print("✗ Web服务器启动失败")
            return False
        
    except Exception as e:
        print(f"✗ Web服务器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_web_pages(plugin):
    """测试Web页面"""
    print("\n" + "=" * 60)
    print("测试Web页面访问")
    print("=" * 60)
    
    try:
        import requests
        
        server_info = plugin.get_server_info()
        base_url = server_info['url']
        
        # 测试页面列表
        pages = [
            ('/', '仪表板主页'),
            ('/testplan', '用例管理页面'),
            ('/bug', 'BUG管理页面'),
            ('/health', '健康检查'),
            ('/api/health', 'API健康检查'),
            ('/api/dashboard/statistics', '统计数据API'),
        ]
        
        success_count = 0
        for path, name in pages:
            try:
                url = base_url + path
                response = requests.get(url, timeout=5)
                
                if response.status_code == 200:
                    print(f"✓ {name}: {response.status_code}")
                    success_count += 1
                else:
                    print(f"⚠ {name}: {response.status_code}")
                    
            except Exception as e:
                print(f"✗ {name}: 连接失败 - {str(e)}")
        
        print(f"\n页面测试结果: {success_count}/{len(pages)} 个页面正常")
        return success_count > len(pages) // 2  # 超过一半页面正常就算成功
        
    except ImportError:
        print("⚠ requests库未安装，跳过页面测试")
        print("可以手动访问页面进行测试")
        return True
    except Exception as e:
        print(f"✗ 页面测试失败: {str(e)}")
        return False

def test_database_operations():
    """测试数据库操作"""
    print("\n" + "=" * 60)
    print("测试数据库操作")
    print("=" * 60)
    
    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        
        # 添加路径
        if dashboard_path not in sys.path:
            sys.path.insert(0, dashboard_path)
        
        try:
            # 运行框架测试
            from test_framework import main as test_framework_main
            result = test_framework_main()
            
            if result:
                print("✓ 数据库操作测试通过")
            else:
                print("⚠ 数据库操作测试部分失败")
            
            return result
            
        finally:
            os.chdir(original_cwd)
        
    except Exception as e:
        print(f"✗ 数据库操作测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def open_dashboard_in_browser(plugin):
    """在浏览器中打开仪表板"""
    print("\n" + "=" * 60)
    print("打开仪表板页面")
    print("=" * 60)
    
    try:
        server_info = plugin.get_server_info()
        if server_info['running']:
            url = server_info['url']
            print(f"正在打开浏览器访问: {url}")
            
            # 延迟打开浏览器，确保服务器完全启动
            def delayed_open():
                time.sleep(2)
                webbrowser.open(url)
            
            threading.Thread(target=delayed_open, daemon=True).start()
            
            print("✓ 浏览器打开请求已发送")
            print(f"✓ 如果浏览器没有自动打开，请手动访问: {url}")
            return True
        else:
            print("✗ 服务器未运行，无法打开页面")
            return False
            
    except Exception as e:
        print(f"✗ 打开浏览器失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("RunSim Dashboard 第一阶段测试")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("阶段目标: 基础框架搭建")
    
    # 测试步骤
    tests = []
    
    # 1. 测试插件加载
    plugin = test_plugin_loading()
    if plugin:
        tests.append(("插件加载", True))
        
        # 2. 测试Web服务器
        server_ok = test_web_server(plugin)
        tests.append(("Web服务器启动", server_ok))
        
        if server_ok:
            # 3. 测试数据库操作
            db_ok = test_database_operations()
            tests.append(("数据库操作", db_ok))
            
            # 4. 测试Web页面
            pages_ok = test_web_pages(plugin)
            tests.append(("Web页面访问", pages_ok))
            
            # 5. 打开仪表板
            browser_ok = open_dashboard_in_browser(plugin)
            tests.append(("浏览器打开", browser_ok))
            
        else:
            tests.extend([
                ("数据库操作", False),
                ("Web页面访问", False),
                ("浏览器打开", False)
            ])
    else:
        tests.extend([
            ("插件加载", False),
            ("Web服务器启动", False),
            ("数据库操作", False),
            ("Web页面访问", False),
            ("浏览器打开", False)
        ])
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("第一阶段测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed >= 4:  # 至少4个测试通过
        print("\n🎉 第一阶段基础框架搭建成功！")
        print("\n✅ 已完成的功能:")
        print("   - 插件系统集成")
        print("   - Flask Web服务器")
        print("   - SQLite数据库")
        print("   - 基础HTML模板")
        print("   - API接口框架")
        print("   - 仪表板主页面")
        
        if plugin and plugin.get_server_info()['running']:
            print(f"\n🌐 仪表板访问地址: {plugin.get_server_info()['url']}")
            print("   可以在浏览器中查看仪表板界面")
            
            print("\n⏭️  下一步: 开始第二阶段 - Excel解析和用例管理")
        
        return True
    else:
        print("\n⚠️  第一阶段测试未完全通过，请检查错误信息并修复问题。")
        return False

if __name__ == '__main__':
    try:
        success = main()
        
        if success:
            print("\n按 Ctrl+C 退出测试...")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n测试结束")
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n测试执行异常: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
