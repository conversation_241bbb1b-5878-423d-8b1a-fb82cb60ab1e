#!/usr/bin/env python3
"""
路由测试脚本

测试Flask应用的路由是否正常工作
"""

import os
import sys
from datetime import datetime

def test_flask_routes():
    """测试Flask路由"""
    print("测试Flask路由")
    print("=" * 40)
    
    try:
        # 切换到正确的目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # 添加当前目录到Python路径
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        # 导入Flask应用
        from app import create_app
        
        # 创建测试应用
        test_config = {
            'DATABASE_PATH': os.path.join('data', 'test_routes.db'),
            'TESTING': True
        }
        
        app = create_app(test_config)
        print("✓ Flask应用创建成功")
        
        # 测试应用上下文
        with app.app_context():
            print("✓ 应用上下文正常")
            
            # 创建测试客户端
            client = app.test_client()
            
            # 测试主页路由
            print("\n测试路由:")
            
            # 测试 / 路由
            response = client.get('/')
            print(f"  GET / : {response.status_code}")
            if response.status_code != 200:
                print(f"    错误: {response.data.decode('utf-8')[:200]}")
            
            # 测试 /testplan 路由
            response = client.get('/testplan')
            print(f"  GET /testplan : {response.status_code}")
            if response.status_code != 200:
                print(f"    错误: {response.data.decode('utf-8')[:200]}")
            else:
                print("    ✓ testplan页面正常")
            
            # 测试 /bug 路由
            response = client.get('/bug')
            print(f"  GET /bug : {response.status_code}")
            if response.status_code != 200:
                print(f"    错误: {response.data.decode('utf-8')[:200]}")
            
            # 测试 /health 路由
            response = client.get('/health')
            print(f"  GET /health : {response.status_code}")
            if response.status_code != 200:
                print(f"    错误: {response.data.decode('utf-8')[:200]}")
            
            # 测试API路由
            response = client.get('/api/testplan/statistics')
            print(f"  GET /api/testplan/statistics : {response.status_code}")
            
            # 列出所有路由
            print("\n所有注册的路由:")
            for rule in app.url_map.iter_rules():
                print(f"  {list(rule.methods)} {rule.rule}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_template_rendering():
    """测试模板渲染"""
    print("\n测试模板渲染")
    print("=" * 40)
    
    try:
        # 切换到正确的目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # 添加当前目录到Python路径
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        from app import create_app
        from flask import render_template
        
        # 创建测试应用
        test_config = {
            'DATABASE_PATH': os.path.join('data', 'test_routes.db'),
            'TESTING': True
        }
        
        app = create_app(test_config)
        
        with app.app_context():
            # 测试各个模板
            templates = [
                'dashboard.html',
                'testplan.html',
                'bug.html',
                'error.html'
            ]
            
            for template_name in templates:
                try:
                    if template_name == 'error.html':
                        # error.html需要参数
                        result = render_template(template_name, 
                                               error_code=404, 
                                               error_message='测试错误')
                    else:
                        result = render_template(template_name)
                    
                    print(f"  ✓ {template_name} 渲染成功 ({len(result)} 字符)")
                    
                except Exception as e:
                    print(f"  ✗ {template_name} 渲染失败: {str(e)}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"✗ 模板测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_static_files():
    """测试静态文件"""
    print("\n测试静态文件结构")
    print("=" * 40)
    
    try:
        # 检查静态文件目录
        static_dir = os.path.join(os.path.dirname(__file__), 'static')
        templates_dir = os.path.join(os.path.dirname(__file__), 'templates')
        
        print(f"静态文件目录: {static_dir}")
        print(f"模板目录: {templates_dir}")
        
        # 检查模板文件
        required_templates = ['base.html', 'dashboard.html', 'testplan.html', 'bug.html', 'error.html']
        for template in required_templates:
            template_path = os.path.join(templates_dir, template)
            if os.path.exists(template_path):
                print(f"  ✓ {template}")
            else:
                print(f"  ✗ {template} 不存在")
                return False
        
        # 确保静态文件目录存在
        os.makedirs(static_dir, exist_ok=True)
        os.makedirs(os.path.join(static_dir, 'css'), exist_ok=True)
        os.makedirs(os.path.join(static_dir, 'js'), exist_ok=True)
        os.makedirs(os.path.join(static_dir, 'uploads'), exist_ok=True)
        
        print("  ✓ 静态文件目录结构正常")
        return True
        
    except Exception as e:
        print(f"✗ 静态文件测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("Flask路由诊断测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查是否在正确的目录
    if not os.path.exists('app.py'):
        print("❌ 错误: 请在dashboard_web目录下运行此脚本")
        return False
    
    # 运行测试
    tests = [
        ("静态文件检查", test_static_files),
        ("模板渲染测试", test_template_rendering),
        ("Flask路由测试", test_flask_routes),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 40)
    print("诊断结果汇总")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！")
        print("\n如果Web页面仍然显示NOT FOUND，可能的原因:")
        print("   1. Flask服务器没有正确启动")
        print("   2. 端口被其他程序占用")
        print("   3. 浏览器缓存问题")
        print("   4. 网络连接问题")
        
        print("\n建议解决方案:")
        print("   1. 重启RunSim GUI")
        print("   2. 清除浏览器缓存")
        print("   3. 检查端口5000是否被占用")
        print("   4. 手动访问 http://127.0.0.1:5000/testplan")
        
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(0)
