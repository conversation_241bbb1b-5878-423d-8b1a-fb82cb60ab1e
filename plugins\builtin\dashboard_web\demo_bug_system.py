#!/usr/bin/env python3
"""
BUG管理系统演示脚本

该脚本演示BUG管理系统的主要功能，包括：
- 创建示例BUG数据
- 展示统计功能
- 演示API调用
"""

import os
import sys
import json
from datetime import datetime, date, timedelta

# 添加路径
sys.path.insert(0, '.')

def create_sample_bugs():
    """创建示例BUG数据"""
    print("=== 创建示例BUG数据 ===")
    
    from models.bug import BugModel
    
    db_path = 'data/dashboard.db'
    bug_model = BugModel(db_path)
    
    # 示例BUG数据
    sample_bugs = [
        {
            'bug_id': 'BUG-2024-001',
            'bug_type': '功能缺陷',
            'description': '用户登录功能在特定条件下失败',
            'severity': 'High',
            'status': 'Open',
            'submitter': '张三',
            'verifier': '李四',
            'submit_sys': '用户管理系统',
            'verification_stage': '子系统级',
            'discovery_platform': 'Windows 10',
            'discovery_case': 'login_test_001',
            'submit_date': (date.today() - timedelta(days=5)).isoformat()
        },
        {
            'bug_id': 'BUG-2024-002',
            'bug_type': '性能问题',
            'description': '数据查询响应时间过长',
            'severity': 'Medium',
            'status': 'In Progress',
            'submitter': '王五',
            'verifier': '赵六',
            'submit_sys': '数据管理系统',
            'verification_stage': 'TOP级',
            'discovery_platform': 'Linux',
            'discovery_case': 'query_performance_test',
            'submit_date': (date.today() - timedelta(days=3)).isoformat()
        },
        {
            'bug_id': 'BUG-2024-003',
            'bug_type': '界面问题',
            'description': '按钮样式在不同浏览器下显示不一致',
            'severity': 'Low',
            'status': 'Fixed',
            'submitter': '孙七',
            'verifier': '周八',
            'submit_sys': 'Web界面',
            'verification_stage': '后仿真',
            'discovery_platform': 'Chrome',
            'discovery_case': 'ui_compatibility_test',
            'submit_date': (date.today() - timedelta(days=7)).isoformat(),
            'fix_date': (date.today() - timedelta(days=1)).isoformat()
        },
        {
            'bug_id': 'BUG-2024-004',
            'bug_type': '安全问题',
            'description': 'SQL注入漏洞',
            'severity': 'Critical',
            'status': 'Fixed',
            'submitter': '安全团队',
            'verifier': '开发团队',
            'submit_sys': '数据库接口',
            'verification_stage': '子系统级',
            'discovery_platform': 'All',
            'discovery_case': 'security_scan_001',
            'submit_date': (date.today() - timedelta(days=10)).isoformat(),
            'fix_date': (date.today() - timedelta(days=2)).isoformat()
        },
        {
            'bug_id': 'BUG-2024-005',
            'bug_type': '兼容性问题',
            'description': '在IE浏览器下功能异常',
            'severity': 'Medium',
            'status': 'Closed',
            'submitter': '测试团队',
            'verifier': '产品团队',
            'submit_sys': 'Web应用',
            'verification_stage': 'TOP级',
            'discovery_platform': 'IE 11',
            'discovery_case': 'browser_compatibility_test',
            'submit_date': (date.today() - timedelta(days=15)).isoformat(),
            'fix_date': (date.today() - timedelta(days=5)).isoformat()
        }
    ]
    
    created_count = 0
    for bug_data in sample_bugs:
        # 检查是否已存在
        existing_bug = bug_model.get_bug_by_bug_id(bug_data['bug_id'])
        if not existing_bug:
            bug_id = bug_model.create_bug(bug_data)
            if bug_id:
                print(f"✓ 创建BUG: {bug_data['bug_id']}")
                created_count += 1
            else:
                print(f"✗ 创建BUG失败: {bug_data['bug_id']}")
        else:
            print(f"- BUG已存在: {bug_data['bug_id']}")
    
    print(f"\n共创建 {created_count} 个新BUG")
    return created_count > 0

def show_bug_statistics():
    """显示BUG统计信息"""
    print("\n=== BUG统计信息 ===")
    
    from models.bug import BugModel
    
    db_path = 'data/dashboard.db'
    bug_model = BugModel(db_path)
    
    # 获取统计信息
    stats = bug_model.get_bug_statistics()
    
    print(f"总BUG数量: {stats['total_bugs']}")
    print(f"未解决BUG: {stats['open_bugs']}")
    print(f"已修复BUG: {stats['fixed_bugs']}")
    print(f"已关闭BUG: {stats['closed_bugs']}")
    
    print("\n状态分布:")
    for status, count in stats['status_distribution'].items():
        print(f"  {status}: {count}")
    
    print("\n严重程度分布:")
    for severity, count in stats['severity_distribution'].items():
        print(f"  {severity}: {count}")
    
    print("\n类型分布:")
    for bug_type, count in stats['type_distribution'].items():
        print(f"  {bug_type}: {count}")

def show_bug_list():
    """显示BUG列表"""
    print("\n=== BUG列表 ===")
    
    from models.bug import BugModel
    
    db_path = 'data/dashboard.db'
    bug_model = BugModel(db_path)
    
    # 获取BUG列表
    bugs, total = bug_model.get_bugs_list(page=1, page_size=10)
    
    print(f"共 {total} 个BUG，显示前10个:")
    print("-" * 80)
    print(f"{'BUG ID':<15} {'类型':<10} {'严重程度':<8} {'状态':<12} {'描述':<30}")
    print("-" * 80)
    
    for bug in bugs:
        description = bug['description'][:27] + '...' if len(bug['description']) > 30 else bug['description']
        print(f"{bug['bug_id']:<15} {bug['bug_type'] or '':<10} {bug['severity']:<8} {bug['status']:<12} {description:<30}")

def test_api_endpoints():
    """测试API端点"""
    print("\n=== 测试API端点 ===")
    
    from app import create_app
    
    app = create_app()
    
    with app.test_client() as client:
        # 测试BUG列表API
        response = client.get('/api/bugs')
        if response.status_code == 200:
            data = response.get_json()
            if data.get('success'):
                print(f"✓ BUG列表API: 返回 {len(data['data']['bugs'])} 个BUG")
            else:
                print(f"✗ BUG列表API: {data.get('message')}")
        else:
            print(f"✗ BUG列表API: HTTP {response.status_code}")
        
        # 测试BUG统计API
        response = client.get('/api/bugs/statistics')
        if response.status_code == 200:
            data = response.get_json()
            if data.get('success'):
                print(f"✓ BUG统计API: 总数 {data['data']['total_bugs']}")
            else:
                print(f"✗ BUG统计API: {data.get('message')}")
        else:
            print(f"✗ BUG统计API: HTTP {response.status_code}")
        
        # 测试BUG趋势API
        response = client.get('/api/bugs/trend?days=7')
        if response.status_code == 200:
            data = response.get_json()
            if data.get('success'):
                trend_data = data['data']
                print(f"✓ BUG趋势API: {len(trend_data['labels'])} 天数据")
            else:
                print(f"✗ BUG趋势API: {data.get('message')}")
        else:
            print(f"✗ BUG趋势API: HTTP {response.status_code}")

def show_trend_data():
    """显示趋势数据"""
    print("\n=== BUG趋势数据 ===")
    
    from models.bug import BugModel
    
    db_path = 'data/dashboard.db'
    bug_model = BugModel(db_path)
    
    # 获取7天趋势数据
    trend = bug_model.get_bug_trend_data(7)
    
    print("最近7天BUG趋势:")
    print("-" * 40)
    print(f"{'日期':<12} {'新增':<6} {'修复':<6}")
    print("-" * 40)
    
    for i, label in enumerate(trend['labels']):
        new_bugs = trend['new_bugs'][i] if i < len(trend['new_bugs']) else 0
        fixed_bugs = trend['fixed_bugs'][i] if i < len(trend['fixed_bugs']) else 0
        print(f"{label:<12} {new_bugs:<6} {fixed_bugs:<6}")

def main():
    """主演示函数"""
    print("BUG管理系统演示")
    print("=" * 50)
    
    # 创建示例数据
    create_sample_bugs()
    
    # 显示统计信息
    show_bug_statistics()
    
    # 显示BUG列表
    show_bug_list()
    
    # 显示趋势数据
    show_trend_data()
    
    # 测试API端点
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("\n要使用BUG管理系统，请:")
    print("1. 启动Flask应用: python app.py")
    print("2. 在浏览器中访问: http://127.0.0.1:5000/bug")
    print("3. 或通过RunSim GUI的仪表板插件访问")

if __name__ == '__main__':
    main()
