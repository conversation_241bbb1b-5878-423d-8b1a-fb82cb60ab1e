#!/usr/bin/env python3
"""
插件修复测试脚本

测试插件加载和Web服务器启动是否正常
"""

import os
import sys
import time
import threading
from datetime import datetime

def test_plugin_import():
    """测试插件导入"""
    print("测试插件导入...")

    try:
        # 添加正确的路径
        current_file = os.path.abspath(__file__)
        builtin_dir = os.path.dirname(current_file)  # plugins/builtin
        plugins_dir = os.path.dirname(builtin_dir)   # plugins
        root_dir = os.path.dirname(plugins_dir)      # runsim根目录

        # 添加根目录到路径，这样可以导入plugins.base
        if root_dir not in sys.path:
            sys.path.insert(0, root_dir)

        # 添加builtin目录到路径，这样可以导入dashboard_plugin
        if builtin_dir not in sys.path:
            sys.path.insert(0, builtin_dir)

        print(f"  添加路径: {root_dir}")
        print(f"  添加路径: {builtin_dir}")

        # 导入插件
        from dashboard_plugin import DashboardPlugin

        # 创建插件实例
        plugin = DashboardPlugin()

        print(f"✓ 插件导入成功")
        print(f"  - 插件名称: {plugin.name}")
        print(f"  - 插件版本: {plugin.version}")
        print(f"  - 插件描述: {plugin.description}")

        return plugin

    except Exception as e:
        print(f"✗ 插件导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_dependencies():
    """测试依赖"""
    print("\n测试依赖...")

    try:
        # 测试Flask
        import flask
        print(f"✓ Flask可用，版本: {flask.__version__}")

        # 测试dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        if os.path.exists(dashboard_path):
            print(f"✓ dashboard_web目录存在: {dashboard_path}")

            # 检查关键文件
            key_files = ['app.py', 'models/database.py', 'routes/api.py']
            for file_name in key_files:
                file_path = os.path.join(dashboard_path, file_name)
                if os.path.exists(file_path):
                    print(f"  ✓ {file_name}")
                else:
                    print(f"  ✗ {file_name} 不存在")
                    return False
        else:
            print(f"✗ dashboard_web目录不存在: {dashboard_path}")
            return False

        return True

    except ImportError as e:
        print(f"✗ 依赖检查失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 依赖检查异常: {e}")
        return False

def test_flask_app():
    """测试Flask应用创建"""
    print("\n测试Flask应用创建...")

    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)

        # 添加路径
        if dashboard_path not in sys.path:
            sys.path.insert(0, dashboard_path)

        try:
            from app import create_app

            # 创建测试应用
            test_config = {
                'DATABASE_PATH': os.path.join('data', 'test_plugin.db'),
                'TESTING': True
            }

            app = create_app(test_config)
            print("✓ Flask应用创建成功")

            # 测试应用上下文
            with app.app_context():
                print("✓ 应用上下文正常")

            return True

        finally:
            os.chdir(original_cwd)

    except Exception as e:
        print(f"✗ Flask应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plugin_initialization():
    """测试插件初始化"""
    print("\n测试插件初始化...")

    try:
        # 确保路径正确设置
        current_file = os.path.abspath(__file__)
        builtin_dir = os.path.dirname(current_file)
        plugins_dir = os.path.dirname(builtin_dir)
        root_dir = os.path.dirname(plugins_dir)

        if root_dir not in sys.path:
            sys.path.insert(0, root_dir)
        if builtin_dir not in sys.path:
            sys.path.insert(0, builtin_dir)

        from dashboard_plugin import DashboardPlugin

        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.tools_menu = MockMenu()

        class MockMenu:
            def addSeparator(self):
                pass
            def addAction(self, action):
                print(f"  ✓ 菜单项已添加: {action.text()}")

        # 模拟PyQt5组件
        import types

        # 创建模拟的PyQt5模块
        mock_qtwidgets = types.ModuleType('PyQt5.QtWidgets')
        mock_qtwidgets.QAction = type('QAction', (), {
            '__init__': lambda self, text, parent: setattr(self, '_text', text),
            'text': lambda self: getattr(self, '_text', ''),
            'setStatusTip': lambda self, tip: None,
            'triggered': lambda self: type('Signal', (), {'connect': lambda self, func: None})()
        })
        mock_qtwidgets.QMessageBox = type('QMessageBox', (), {
            'warning': lambda *args: print(f"  模拟警告: {args[-1]}")
        })

        mock_qtcore = types.ModuleType('PyQt5.QtCore')
        mock_qtcore.QTimer = type('QTimer', (), {
            '__init__': lambda self: None,
            'timeout': type('Signal', (), {'connect': lambda self, func: None})(),
            'start': lambda self, interval: None,
            'stop': lambda self: None,
            'isActive': lambda self: False
        })

        sys.modules['PyQt5.QtWidgets'] = mock_qtwidgets
        sys.modules['PyQt5.QtCore'] = mock_qtcore

        # 创建插件实例
        plugin = DashboardPlugin()
        main_window = MockMainWindow()

        # 初始化插件（不启动Web服务器）
        plugin.main_window = main_window
        plugin._create_menu_action()

        print("✓ 插件初始化成功（不包含Web服务器）")

        # 测试依赖检查
        if plugin._check_dependencies():
            print("✓ 依赖检查通过")
            return True
        else:
            print("✗ 依赖检查失败")
            return False

    except Exception as e:
        print(f"✗ 插件初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("插件修复测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 测试步骤
    tests = [
        ("依赖检查", test_dependencies),
        ("插件导入", test_plugin_import),
        ("Flask应用创建", test_flask_app),
        ("插件初始化", test_plugin_initialization),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            if test_name == "插件导入":
                result = test_func()
                results.append((test_name, result is not None))
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))

    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("\n🎉 插件修复成功！")
        print("\n✅ 修复内容:")
        print("   - 模块导入路径问题")
        print("   - 线程中的模块访问问题")
        print("   - Flask应用创建问题")
        print("   - 依赖检查机制")

        print("\n🚀 现在可以:")
        print("   1. 在RunSim GUI中加载仪表板插件")
        print("   2. 点击工具菜单中的'项目仪表板'")
        print("   3. 浏览器将自动打开仪表板页面")

        return True
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
