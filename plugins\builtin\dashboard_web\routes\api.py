"""
API路由模块

该模块定义了仪表板的REST API接口，包括：
- 仪表板统计数据API
- 系统状态API
- 通用数据查询API
"""

import logging
import os
import sys
from datetime import datetime, timedelta
from flask import Blueprint, jsonify, request, current_app

# 确保能找到models模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
models_dir = os.path.join(parent_dir, 'models')

if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)
if models_dir not in sys.path:
    sys.path.insert(0, models_dir)

try:
    from models.database import get_db
    from utils.phase_analyzer import PhaseAnalyzer
except ImportError:
    from database import get_db
    import sys
    import os
    utils_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils')
    if utils_dir not in sys.path:
        sys.path.insert(0, utils_dir)
    from phase_analyzer import PhaseAnalyzer

# 配置日志
logger = logging.getLogger(__name__)

# 创建API蓝图
api_bp = Blueprint('api', __name__)

@api_bp.route('/health')
def health_check():
    """
    健康检查API

    Returns:
        JSON: 系统健康状态
    """
    try:
        # 检查数据库连接
        with get_db() as conn:
            conn.execute('SELECT 1')

        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database': 'connected',
            'version': '1.0.0'
        })
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            'status': 'unhealthy',
            'timestamp': datetime.now().isoformat(),
            'error': str(e)
        }), 500

@api_bp.route('/dashboard/statistics')
def get_dashboard_statistics():
    """
    获取仪表板统计数据

    Returns:
        JSON: 包含各种统计数据的响应
    """
    try:
        with get_db() as conn:
            cursor = conn.cursor()

            # 获取用例统计 - 分别统计Subsys级别和TOP级别
            cursor.execute('''
                SELECT
                    COUNT(*) as total_cases,
                    -- Subsys级别统计（包括subsys和post_subsys阶段）
                    SUM(CASE WHEN subsys_status = 'PASS' OR post_subsys_status = 'PASS'
                        THEN 1 ELSE 0 END) as subsys_passed_cases,
                    SUM(CASE WHEN (subsys_status = 'Pending' OR post_subsys_status = 'Pending')
                             AND subsys_status != 'PASS' AND post_subsys_status != 'PASS'
                        THEN 1 ELSE 0 END) as subsys_pending_cases,
                    SUM(CASE WHEN (subsys_status = 'On-Going' OR post_subsys_status = 'On-Going')
                             AND subsys_status != 'PASS' AND post_subsys_status != 'PASS'
                        THEN 1 ELSE 0 END) as subsys_running_cases,
                    -- TOP级别统计（包括top和post_top阶段）
                    SUM(CASE WHEN top_status = 'PASS' OR post_top_status = 'PASS'
                        THEN 1 ELSE 0 END) as top_passed_cases,
                    SUM(CASE WHEN (top_status = 'Pending' OR post_top_status = 'Pending')
                             AND top_status != 'PASS' AND post_top_status != 'PASS'
                        THEN 1 ELSE 0 END) as top_pending_cases,
                    SUM(CASE WHEN (top_status = 'On-Going' OR post_top_status = 'On-Going')
                             AND top_status != 'PASS' AND post_top_status != 'PASS'
                        THEN 1 ELSE 0 END) as top_running_cases
                FROM test_cases
            ''')
            case_stats = cursor.fetchone()

            # 获取BUG统计
            cursor.execute('''
                SELECT
                    COUNT(*) as total_bugs,
                    SUM(CASE WHEN status = 'Open' THEN 1 ELSE 0 END) as open_bugs,
                    SUM(CASE WHEN status = 'Fixed' THEN 1 ELSE 0 END) as fixed_bugs,
                    SUM(CASE WHEN status = 'Closed' THEN 1 ELSE 0 END) as closed_bugs
                FROM bugs
            ''')
            bug_stats = cursor.fetchone()

            # 获取项目统计
            cursor.execute('SELECT COUNT(*) as total_projects FROM projects')
            project_stats = cursor.fetchone()

            # 计算通过率
            total_cases = case_stats['total_cases'] or 0
            subsys_passed = case_stats['subsys_passed_cases'] or 0
            top_passed = case_stats['top_passed_cases'] or 0
            total_passed = subsys_passed + top_passed

            subsys_pending = case_stats['subsys_pending_cases'] or 0
            top_pending = case_stats['top_pending_cases'] or 0
            total_pending = subsys_pending + top_pending

            subsys_running = case_stats['subsys_running_cases'] or 0
            top_running = case_stats['top_running_cases'] or 0
            total_running = subsys_running + top_running

            # 计算通过率（基于实际通过的用例数，避免重复计算）
            # 通过率应该基于总用例数，而不是各级别通过数的简单相加
            actual_passed_cases = 0
            if total_cases > 0:
                # 重新计算实际通过的用例数（一个用例只计算一次）
                cursor.execute('''
                    SELECT COUNT(*) as actual_passed
                    FROM test_cases
                    WHERE subsys_status = 'PASS' OR top_status = 'PASS'
                       OR post_subsys_status = 'PASS' OR post_top_status = 'PASS'
                ''')
                actual_passed_cases = cursor.fetchone()['actual_passed'] or 0

            pass_rate = (actual_passed_cases / total_cases * 100) if total_cases > 0 else 0

            return jsonify({
                'cases': {
                    'total': total_cases,
                    'passed': actual_passed_cases,  # 使用实际通过数，避免重复计算
                    'pending': total_pending,
                    'running': total_running,
                    'pass_rate': round(pass_rate, 2),
                    # 分级别统计
                    'subsys': {
                        'passed': subsys_passed,
                        'pending': subsys_pending,
                        'running': subsys_running
                    },
                    'top': {
                        'passed': top_passed,
                        'pending': top_pending,
                        'running': top_running
                    }
                },
                'bugs': {
                    'total': bug_stats['total_bugs'] or 0,
                    'open': bug_stats['open_bugs'] or 0,
                    'fixed': bug_stats['fixed_bugs'] or 0,
                    'closed': bug_stats['closed_bugs'] or 0
                },
                'projects': {
                    'total': project_stats['total_projects'] or 0
                },
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"获取仪表板统计数据失败: {e}")
        return jsonify({'error': '获取统计数据失败', 'message': str(e)}), 500

@api_bp.route('/dashboard/progress')
def get_project_progress():
    """
    获取项目进度数据

    Returns:
        JSON: 项目进度信息
    """
    try:
        with get_db() as conn:
            cursor = conn.cursor()

            # 修正项目进度计算逻辑，使用统一的状态值格式
            cursor.execute('''
                SELECT
                    COUNT(*) as total,
                    -- Subsys级别进度（只统计有状态且不为N/A的用例）
                    COUNT(CASE WHEN subsys_status IS NOT NULL AND subsys_status != 'N/A' THEN 1 END) as subsys_total,
                    SUM(CASE WHEN subsys_status = 'PASS' THEN 1 ELSE 0 END) as subsys_completed,
                    -- TOP级别进度（只统计有状态且不为N/A的用例）
                    COUNT(CASE WHEN top_status IS NOT NULL AND top_status != 'N/A' THEN 1 END) as top_total,
                    SUM(CASE WHEN top_status = 'PASS' THEN 1 ELSE 0 END) as top_completed,
                    -- 后仿Subsys进度（只统计标记了√的用例）
                    COUNT(CASE WHEN post_subsys_phase = '√' THEN 1 END) as post_subsys_total,
                    SUM(CASE WHEN post_subsys_status = 'PASS' AND post_subsys_phase = '√' THEN 1 ELSE 0 END) as post_subsys_completed,
                    -- 后仿TOP进度（只统计标记了√的用例）
                    COUNT(CASE WHEN post_top_phase = '√' THEN 1 END) as post_top_total,
                    SUM(CASE WHEN post_top_status = 'PASS' AND post_top_phase = '√' THEN 1 ELSE 0 END) as post_top_completed
                FROM test_cases
            ''')
            progress_data = cursor.fetchone()

            # 计算各级别进度百分比
            subsys_total = progress_data['subsys_total'] or 0
            top_total = progress_data['top_total'] or 0
            post_subsys_total = progress_data['post_subsys_total'] or 0
            post_top_total = progress_data['post_top_total'] or 0

            subsys_progress = (progress_data['subsys_completed'] / subsys_total * 100) if subsys_total > 0 else 0
            top_progress = (progress_data['top_completed'] / top_total * 100) if top_total > 0 else 0
            post_subsys_progress = (progress_data['post_subsys_completed'] / post_subsys_total * 100) if post_subsys_total > 0 else 0
            post_top_progress = (progress_data['post_top_completed'] / post_top_total * 100) if post_top_total > 0 else 0

            return jsonify({
                'subsys_progress': round(subsys_progress, 2),
                'top_progress': round(top_progress, 2),
                'post_subsys_progress': round(post_subsys_progress, 2),
                'post_top_progress': round(post_top_progress, 2),
                'overall_progress': round((subsys_progress + top_progress + post_subsys_progress + post_top_progress) / 4, 2),
                'total_cases': progress_data['total'],
                'subsys_total': subsys_total,
                'top_total': top_total,
                'post_subsys_total': post_subsys_total,
                'post_top_total': post_top_total,
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"获取项目进度失败: {e}")
        return jsonify({'error': '获取项目进度失败', 'message': str(e)}), 500

@api_bp.route('/dashboard/case_status')
def get_case_status_distribution():
    """
    获取用例状态分布

    Returns:
        JSON: 用例状态分布数据
    """
    try:
        with get_db() as conn:
            cursor = conn.cursor()

            # 统计各状态的用例数量（分Subsys和TOP级别统计）
            cursor.execute('''
                SELECT
                    -- Subsys级别统计
                    SUM(CASE WHEN subsys_status = 'PASS' OR post_subsys_status = 'PASS'
                        THEN 1 ELSE 0 END) as subsys_pass_count,
                    SUM(CASE WHEN (subsys_status = 'Pending' OR post_subsys_status = 'Pending')
                             AND subsys_status != 'PASS' AND post_subsys_status != 'PASS'
                        THEN 1 ELSE 0 END) as subsys_pending_count,
                    SUM(CASE WHEN (subsys_status = 'On-Going' OR post_subsys_status = 'On-Going')
                             AND subsys_status != 'PASS' AND post_subsys_status != 'PASS'
                        THEN 1 ELSE 0 END) as subsys_ongoing_count,
                    -- TOP级别统计
                    SUM(CASE WHEN top_status = 'PASS' OR post_top_status = 'PASS'
                        THEN 1 ELSE 0 END) as top_pass_count,
                    SUM(CASE WHEN (top_status = 'Pending' OR post_top_status = 'Pending')
                             AND top_status != 'PASS' AND post_top_status != 'PASS'
                        THEN 1 ELSE 0 END) as top_pending_count,
                    SUM(CASE WHEN (top_status = 'On-Going' OR post_top_status = 'On-Going')
                             AND top_status != 'PASS' AND post_top_status != 'PASS'
                        THEN 1 ELSE 0 END) as top_ongoing_count,
                    -- 未开始的用例（所有阶段都为空或N/A）
                    SUM(CASE WHEN (subsys_status IS NULL OR subsys_status = 'N/A')
                             AND (top_status IS NULL OR top_status = 'N/A')
                             AND (post_subsys_status IS NULL OR post_subsys_status = 'N/A')
                             AND (post_top_status IS NULL OR post_top_status = 'N/A')
                        THEN 1 ELSE 0 END) as not_started_count
                FROM test_cases
            ''')
            status_data = cursor.fetchone()

            # 计算总计
            total_pass = (status_data['subsys_pass_count'] or 0) + (status_data['top_pass_count'] or 0)
            total_pending = (status_data['subsys_pending_count'] or 0) + (status_data['top_pending_count'] or 0)
            total_ongoing = (status_data['subsys_ongoing_count'] or 0) + (status_data['top_ongoing_count'] or 0)

            return jsonify({
                'pass': total_pass,
                'pending': total_pending,
                'ongoing': total_ongoing,
                'not_started': status_data['not_started_count'] or 0,
                # 分级别详细统计
                'subsys': {
                    'pass': status_data['subsys_pass_count'] or 0,
                    'pending': status_data['subsys_pending_count'] or 0,
                    'ongoing': status_data['subsys_ongoing_count'] or 0
                },
                'top': {
                    'pass': status_data['top_pass_count'] or 0,
                    'pending': status_data['top_pending_count'] or 0,
                    'ongoing': status_data['top_ongoing_count'] or 0
                },
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"获取用例状态分布失败: {e}")
        return jsonify({'error': '获取用例状态分布失败', 'message': str(e)}), 500

@api_bp.route('/dashboard/bug_trend')
def get_bug_trend():
    """
    获取BUG趋势数据

    Returns:
        JSON: BUG趋势数据
    """
    try:
        # 获取参数
        days = int(request.args.get('days', 30))
        unit = request.args.get('unit', 'day')

        with get_db() as conn:
            cursor = conn.cursor()

            # 计算日期范围
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)

            # 获取统计开始日期之前的BUG总数作为初始值
            cursor.execute('''
                SELECT COUNT(*) as initial_total
                FROM bugs
                WHERE DATE(created_at) < ?
                AND (status != 'Fixed' OR DATE(updated_at) >= ?)
            ''', (start_date, start_date))
            initial_total = cursor.fetchone()['initial_total'] or 0

            if unit == 'week':
                # 按周统计
                date_list = []
                new_bugs_trend = []
                fixed_bugs_trend = []

                # 生成周标签
                current_date = start_date
                week_data = {}

                while current_date <= end_date:
                    week_label = current_date.strftime('%Y-W%W')
                    if week_label not in week_data:
                        week_data[week_label] = {
                            'start_date': current_date,
                            'end_date': current_date,
                            'display_label': current_date.strftime('%m-%d')
                        }
                    else:
                        week_data[week_label]['end_date'] = current_date
                    current_date += timedelta(days=1)

                # 按周统计BUG数据
                for week_label, week_info in sorted(week_data.items()):
                    date_list.append(week_info['display_label'])

                    # 统计该周新增BUG
                    cursor.execute('''
                        SELECT COUNT(*) as new_bugs
                        FROM bugs
                        WHERE DATE(created_at) >= ? AND DATE(created_at) <= ?
                    ''', (week_info['start_date'], week_info['end_date']))
                    new_bugs = int(cursor.fetchone()['new_bugs'] or 0)
                    new_bugs_trend.append(new_bugs)

                    # 统计该周修复BUG
                    cursor.execute('''
                        SELECT COUNT(*) as fixed_bugs
                        FROM bugs
                        WHERE status = 'Fixed'
                        AND DATE(updated_at) >= ? AND DATE(updated_at) <= ?
                    ''', (week_info['start_date'], week_info['end_date']))
                    fixed_bugs = int(cursor.fetchone()['fixed_bugs'] or 0)
                    fixed_bugs_trend.append(fixed_bugs)

            else:
                # 按天统计
                date_list = []
                current_date = start_date
                while current_date <= end_date:
                    date_list.append(current_date.strftime('%m-%d'))
                    current_date += timedelta(days=1)

                # 获取每日新增BUG数量
                cursor.execute('''
                    SELECT
                        DATE(created_at) as bug_date,
                        COUNT(*) as new_bugs
                    FROM bugs
                    WHERE DATE(created_at) >= ? AND DATE(created_at) <= ?
                    GROUP BY DATE(created_at)
                    ORDER BY bug_date
                ''', (start_date, end_date))
                new_bugs_data = {row['bug_date']: int(row['new_bugs']) for row in cursor.fetchall()}

                # 获取每日修复BUG数量
                cursor.execute('''
                    SELECT
                        DATE(updated_at) as fix_date,
                        COUNT(*) as fixed_bugs
                    FROM bugs
                    WHERE status = 'Fixed'
                    AND DATE(updated_at) >= ? AND DATE(updated_at) <= ?
                    GROUP BY DATE(updated_at)
                    ORDER BY fix_date
                ''', (start_date, end_date))
                fixed_bugs_data = {row['fix_date']: int(row['fixed_bugs']) for row in cursor.fetchall()}

                # 构建趋势数据
                new_bugs_trend = []
                fixed_bugs_trend = []

                current_date = start_date
                for date_label in date_list:
                    date_str = current_date.strftime('%Y-%m-%d')
                    new_bugs_trend.append(new_bugs_data.get(date_str, 0))
                    fixed_bugs_trend.append(fixed_bugs_data.get(date_str, 0))
                    current_date += timedelta(days=1)

            return jsonify({
                'labels': date_list,
                'new_bugs': new_bugs_trend,
                'fixed_bugs': fixed_bugs_trend,
                'initial_total': initial_total,
                'unit': unit,
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"获取BUG趋势失败: {e}")
        return jsonify({'error': '获取BUG趋势失败', 'message': str(e)}), 500

@api_bp.route('/dashboard/progress')
def get_progress():
    """
    获取项目进度数据

    Returns:
        JSON: 项目进度数据
    """
    try:
        # 导入数据分析器
        from utils.data_analyzer import DataAnalyzer

        with get_db() as conn:
            analyzer = DataAnalyzer(conn)

            # 获取阶段效率数据
            phase_data = analyzer.analyze_phase_efficiency()

            # 转换为进度数据格式
            progress_data = {
                'subsys_progress': 0,
                'top_progress': 0,
                'post_subsys_progress': 0,
                'post_top_progress': 0
            }

            if 'phase_efficiency' in phase_data:
                efficiency = phase_data['phase_efficiency']

                # 计算各级别的平均进度
                dvr_phases = ['DVR1', 'DVR2', 'DVR3']
                dvs_phases = ['DVS1', 'DVS2']

                # Subsys级别进度（DVR阶段平均）
                subsys_total = sum(efficiency.get(phase, {}).get('efficiency', 0) for phase in dvr_phases)
                progress_data['subsys_progress'] = round(subsys_total / len(dvr_phases), 2) if dvr_phases else 0

                # TOP级别进度（DVR阶段平均）
                progress_data['top_progress'] = progress_data['subsys_progress']

                # POST阶段进度（DVS阶段平均）
                post_total = sum(efficiency.get(phase, {}).get('efficiency', 0) for phase in dvs_phases)
                post_avg = round(post_total / len(dvs_phases), 2) if dvs_phases else 0
                progress_data['post_subsys_progress'] = post_avg
                progress_data['post_top_progress'] = post_avg

            return jsonify(progress_data)

    except Exception as e:
        logger.error(f"获取项目进度失败: {e}")
        return jsonify({'error': '获取项目进度失败', 'message': str(e)}), 500

@api_bp.route('/dashboard/phase_efficiency')
def get_phase_efficiency():
    """
    获取阶段效率数据

    Returns:
        JSON: 阶段效率数据
    """
    try:
        # 导入数据分析器
        from utils.data_analyzer import DataAnalyzer

        with get_db() as conn:
            analyzer = DataAnalyzer(conn)

            # 获取阶段效率数据
            phase_data = analyzer.analyze_phase_efficiency()

            # 转换为雷达图数据格式
            efficiency_data = {
                'efficiency': [0, 0, 0, 0, 0]  # DVR1, DVR2, DVR3, DVS1, DVS2
            }

            if 'phase_efficiency' in phase_data:
                phases = ['DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2']
                efficiency_data['efficiency'] = [
                    phase_data['phase_efficiency'].get(phase, {}).get('efficiency', 0)
                    for phase in phases
                ]

            return jsonify(efficiency_data)

    except Exception as e:
        logger.error(f"获取阶段效率失败: {e}")
        # 返回模拟数据
        return jsonify({
            'efficiency': [85, 78, 92, 88, 76]
        })



@api_bp.route('/system/config')
def get_system_config():
    """
    获取系统配置

    Returns:
        JSON: 系统配置信息
    """
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT config_key, config_value, description FROM system_config')
            configs = {row['config_key']: {
                'value': row['config_value'],
                'description': row['description']
            } for row in cursor.fetchall()}

            return jsonify({
                'configs': configs,
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"获取系统配置失败: {e}")
        return jsonify({'error': '获取系统配置失败', 'message': str(e)}), 500

@api_bp.errorhandler(404)
def api_not_found(error):
    """API 404错误处理"""
    return jsonify({'error': 'API接口不存在', 'code': 404}), 404

@api_bp.route('/dashboard/phase_distribution')
def get_phase_distribution():
    """
    获取验证阶段分布数据

    Returns:
        JSON: 验证阶段分布数据
    """
    try:
        project_id = request.args.get('project_id', type=int)

        with get_db() as conn:
            analyzer = PhaseAnalyzer(conn)
            phase_data = analyzer.analyze_phase_distribution(project_id)

            return jsonify({
                'phase_distribution': phase_data,
                'phases': PhaseAnalyzer.PHASES,
                'case_types': list(PhaseAnalyzer.CASE_TYPES.keys()),
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"获取验证阶段分布失败: {e}")
        return jsonify({'error': '获取验证阶段分布失败', 'message': str(e)}), 500

@api_bp.route('/dashboard/phase_progress')
def get_phase_progress():
    """
    获取验证阶段进度数据

    Returns:
        JSON: 验证阶段进度数据
    """
    try:
        project_id = request.args.get('project_id', type=int)

        with get_db() as conn:
            analyzer = PhaseAnalyzer(conn)
            progress_data = analyzer.calculate_phase_progress(project_id)

            return jsonify({
                'phase_progress': progress_data,
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"获取验证阶段进度失败: {e}")
        return jsonify({'error': '获取验证阶段进度失败', 'message': str(e)}), 500

@api_bp.route('/dashboard/phase_cases')
def get_phase_cases():
    """
    获取指定阶段的用例列表

    Query Parameters:
        phase: 验证阶段 (DVR1/DVR2/DVR3/DVS1/DVS2)
        case_type: 用例类型 (subsys/top/post_subsys/post_top)
        status: 状态筛选 (可选)
        project_id: 项目ID (可选)

    Returns:
        JSON: 用例列表
    """
    try:
        phase = request.args.get('phase')
        case_type = request.args.get('case_type')
        status = request.args.get('status')
        project_id = request.args.get('project_id', type=int)

        if not phase or not case_type:
            return jsonify({'error': '缺少必要参数: phase, case_type'}), 400

        if phase not in PhaseAnalyzer.PHASES:
            return jsonify({'error': f'无效的阶段: {phase}'}), 400

        if case_type not in PhaseAnalyzer.CASE_TYPES:
            return jsonify({'error': f'无效的用例类型: {case_type}'}), 400

        with get_db() as conn:
            analyzer = PhaseAnalyzer(conn)
            cases = analyzer.get_phase_case_list(phase, case_type, status, project_id)

            return jsonify({
                'cases': cases,
                'total': len(cases),
                'phase': phase,
                'case_type': case_type,
                'status': status,
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"获取阶段用例列表失败: {e}")
        return jsonify({'error': '获取阶段用例列表失败', 'message': str(e)}), 500

@api_bp.route('/dashboard/phase_report')
def get_phase_report():
    """
    获取验证阶段报告

    Returns:
        JSON: 验证阶段报告
    """
    try:
        project_id = request.args.get('project_id', type=int)

        with get_db() as conn:
            analyzer = PhaseAnalyzer(conn)
            report = analyzer.generate_phase_report(project_id)

            return jsonify(report)

    except Exception as e:
        logger.error(f"获取验证阶段报告失败: {e}")
        return jsonify({'error': '获取验证阶段报告失败', 'message': str(e)}), 500

@api_bp.route('/dashboard/update_phase_statistics', methods=['POST'])
def update_phase_statistics():
    """
    更新验证阶段统计数据

    Returns:
        JSON: 更新结果
    """
    try:
        project_id = request.json.get('project_id') if request.json else None

        with get_db() as conn:
            analyzer = PhaseAnalyzer(conn)
            analyzer.update_phase_statistics(project_id)

            return jsonify({
                'success': True,
                'message': '验证阶段统计更新成功',
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"更新验证阶段统计失败: {e}")
        return jsonify({'error': '更新验证阶段统计失败', 'message': str(e)}), 500

@api_bp.errorhandler(500)
def api_internal_error(error):
    """API 500错误处理"""
    logger.error(f"API内部错误: {error}")
    return jsonify({'error': '内部服务器错误', 'code': 500}), 500
