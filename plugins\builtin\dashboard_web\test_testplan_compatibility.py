#!/usr/bin/env python3
"""
TestPlan兼容性测试脚本

该脚本用于测试修改后的TestPlan格式是否与仪表盘功能兼容。
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime

def test_excel_parser():
    """测试Excel解析器"""
    print("1. 测试Excel解析器...")

    try:
        # 导入解析器
        sys.path.insert(0, os.path.dirname(__file__))
        from utils.excel_parser import TestPlanParser

        # 检查列映射
        parser = TestPlanParser()

        # 验证新字段映射
        expected_mappings = {
            'test areas': 'test_areas',
            'test scope': 'test_scope',
            'cover': 'cover',
            'testcase name': 'case_name'
        }

        for key, expected_value in expected_mappings.items():
            if key in parser.COLUMN_MAPPING:
                actual_value = parser.COLUMN_MAPPING[key]
                if actual_value == expected_value:
                    print(f"   ✓ {key} -> {actual_value}")
                else:
                    print(f"   ✗ {key} -> {actual_value} (期望: {expected_value})")
                    return False
            else:
                print(f"   ✗ 缺少映射: {key}")
                return False

        # 验证兼容性映射
        compat_mappings = {
            'test_process': 'test_process',
            'coverage_point': 'coverage_point',
            '用例名称': 'case_name'
        }

        for key, expected_value in compat_mappings.items():
            if key in parser.COLUMN_MAPPING:
                actual_value = parser.COLUMN_MAPPING[key]
                if actual_value == expected_value:
                    print(f"   ✓ 兼容性映射: {key} -> {actual_value}")
                else:
                    print(f"   ✗ 兼容性映射错误: {key} -> {actual_value}")
                    return False

        print("   ✅ Excel解析器测试通过")
        return True

    except Exception as e:
        print(f"   ❌ Excel解析器测试失败: {str(e)}")
        return False

def test_database_model():
    """测试数据库模型"""
    print("2. 测试数据库模型...")

    try:
        from models.database import init_database, DatabaseManager
        import sqlite3

        # 创建临时数据库
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            test_db = tmp_file.name

        try:
            # 初始化数据库
            if not init_database(test_db):
                print("   ❌ 数据库初始化失败")
                return False

            print("   ✓ 数据库初始化成功")

            # 直接使用SQLite测试数据库结构
            with sqlite3.connect(test_db) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 检查表结构
                cursor.execute("PRAGMA table_info(test_cases)")
                columns = [row[1] for row in cursor.fetchall()]

                required_columns = ['test_areas', 'cover', 'case_name', 'test_scope']
                for col in required_columns:
                    if col in columns:
                        print(f"   ✓ 字段存在: {col}")
                    else:
                        print(f"   ✗ 缺少字段: {col}")
                        return False

                # 测试数据插入
                test_case_data = (
                    1,  # project_id
                    'TEST',  # category
                    'TEST_001',  # number
                    'Test Areas Example',  # test_areas
                    'Test Scope Example',  # test_scope
                    'Test Function',  # function_point
                    'Test Check Point',  # check_point
                    'Test Cover',  # cover
                    'test_case_001',  # case_name
                    None,  # start_time
                    None,  # end_time
                    None,  # actual_time
                    '',  # subsys_stage
                    'Pass',  # subsys_status
                    '',  # top_stage
                    'Not Started',  # top_status
                    '',  # post_subsys_stage
                    'Not Started',  # post_subsys_status
                    '',  # post_top_stage
                    'Not Started',  # post_top_status
                    '',  # remarks
                    '',  # test_process
                    ''   # coverage_point
                )

                cursor.execute('''
                    INSERT INTO test_cases (
                        project_id, category, number, test_areas, test_scope, function_point,
                        check_point, cover, case_name, start_time, end_time, actual_time,
                        subsys_stage, subsys_status, top_stage, top_status,
                        post_subsys_stage, post_subsys_status, post_top_stage, post_top_status,
                        remarks, test_process, coverage_point
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', test_case_data)

                conn.commit()
                print("   ✓ 用例数据插入成功")

                # 测试数据查询
                cursor.execute("SELECT * FROM test_cases WHERE case_name = ?", ('test_case_001',))
                case = cursor.fetchone()

                if case:
                    print("   ✓ 用例查询成功")

                    # 验证新字段
                    if case['test_areas'] == 'Test Areas Example':
                        print("   ✓ test_areas字段正确")
                    else:
                        print(f"   ✗ test_areas字段错误: {case['test_areas']}")
                        return False

                    if case['cover'] == 'Test Cover':
                        print("   ✓ cover字段正确")
                    else:
                        print(f"   ✗ cover字段错误: {case['cover']}")
                        return False
                else:
                    print("   ✗ 用例查询失败")
                    return False

            print("   ✅ 数据库模型测试通过")
            return True

        finally:
            # 清理临时文件
            try:
                if os.path.exists(test_db):
                    os.unlink(test_db)
            except PermissionError:
                # Windows上可能出现文件被占用的情况，忽略这个错误
                pass

    except Exception as e:
        print(f"   ❌ 数据库模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_testplan_template():
    """测试TestPlan模板"""
    print("3. 测试TestPlan模板...")

    try:
        # 检查模板文件是否存在
        template_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'TestPlan_Template.xlsx')
        template_path = os.path.abspath(template_path)

        if not os.path.exists(template_path):
            print(f"   ⚠️  模板文件不存在: {template_path}")
            print("   请确保已生成最新的TestPlan模板")
            return True  # 不算失败，只是提醒

        # 尝试解析模板文件
        from utils.excel_parser import TestPlanParser

        parser = TestPlanParser()
        test_cases, parse_info = parser.parse_file(template_path, 'TP')

        if len(test_cases) > 0:
            print(f"   ✓ 模板解析成功，共解析 {len(test_cases)} 条用例")

            # 检查第一个用例的字段
            first_case = test_cases[0]
            required_fields = ['case_name', 'test_areas', 'test_scope', 'cover']

            for field in required_fields:
                if field in first_case:
                    print(f"   ✓ 字段存在: {field} = {first_case[field]}")
                else:
                    print(f"   ✗ 缺少字段: {field}")
                    return False
        else:
            print("   ✗ 模板解析失败，未找到用例数据")
            return False

        print("   ✅ TestPlan模板测试通过")
        return True

    except Exception as e:
        print(f"   ❌ TestPlan模板测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("TestPlan兼容性测试")
    print("=" * 50)

    # 切换到正确的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)

    # 运行测试
    tests = [
        test_excel_parser,
        test_database_model,
        test_testplan_template
    ]

    passed = 0
    total = len(tests)

    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"   ❌ 测试异常: {str(e)}")
            print()

    # 输出结果
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！TestPlan格式兼容性良好")
        print("\n✅ 仪表盘功能已更新以支持新的TestPlan模板格式:")
        print("   - C列: Test Areas (测试区域)")
        print("   - E列: Test Scope (测试范围)")
        print("   - G列: Cover (覆盖)")
        print("   - H列: TestCase Name (测试用例名称)")
        print("\n📝 兼容性说明:")
        print("   - 保留了旧字段以确保向后兼容")
        print("   - 支持新旧两种格式的Excel文件导入")
        print("   - 数据库结构已更新以支持新字段")
    else:
        print("❌ 部分测试失败，请检查相关配置")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
