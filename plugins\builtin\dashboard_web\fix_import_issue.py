#!/usr/bin/env python3
"""
修复TestPlan导入问题的脚本
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_database_structure():
    """修复数据库结构"""
    print("1. 修复数据库结构...")
    
    try:
        dashboard_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(dashboard_dir, 'data', 'dashboard.db')
        
        # 确保数据目录存在
        data_dir = os.path.dirname(db_path)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir, exist_ok=True)
            print(f"   ✅ 创建数据目录: {data_dir}")
        
        # 初始化数据库
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查test_cases表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_cases'")
            if not cursor.fetchone():
                print("   🔧 创建test_cases表...")
                cursor.execute('''
                    CREATE TABLE test_cases (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        project_id INTEGER DEFAULT 1,
                        category TEXT,
                        number TEXT,
                        test_areas TEXT,
                        test_scope TEXT,
                        function_point TEXT,
                        check_point TEXT,
                        cover TEXT,
                        case_name TEXT NOT NULL,
                        start_time TIMESTAMP,
                        end_time TIMESTAMP,
                        actual_time INTEGER,
                        subsys_stage TEXT,
                        subsys_status TEXT DEFAULT 'Not Started',
                        top_stage TEXT,
                        top_status TEXT DEFAULT 'Not Started',
                        post_subsys_stage TEXT,
                        post_subsys_status TEXT DEFAULT 'Not Started',
                        post_top_stage TEXT,
                        post_top_status TEXT DEFAULT 'Not Started',
                        remarks TEXT,
                        test_process TEXT,
                        coverage_point TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                print("   ✅ test_cases表创建成功")
            else:
                print("   ✅ test_cases表已存在")
                
                # 检查并添加缺失的字段
                cursor.execute("PRAGMA table_info(test_cases)")
                existing_columns = [row[1] for row in cursor.fetchall()]
                
                required_columns = [
                    ('test_areas', 'TEXT'),
                    ('cover', 'TEXT'),
                    ('test_process', 'TEXT'),
                    ('coverage_point', 'TEXT')
                ]
                
                for column_name, column_type in required_columns:
                    if column_name not in existing_columns:
                        print(f"   🔧 添加字段: {column_name}")
                        cursor.execute(f"ALTER TABLE test_cases ADD COLUMN {column_name} {column_type}")
                    else:
                        print(f"   ✅ 字段已存在: {column_name}")
            
            # 检查projects表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='projects'")
            if not cursor.fetchone():
                print("   🔧 创建projects表...")
                cursor.execute('''
                    CREATE TABLE projects (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 插入默认项目
                cursor.execute('''
                    INSERT INTO projects (id, name, description) 
                    VALUES (1, 'Default Project', 'Default project for test cases')
                ''')
                print("   ✅ projects表创建成功，已添加默认项目")
            else:
                print("   ✅ projects表已存在")
            
            conn.commit()
            
        print("   ✅ 数据库结构修复完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库结构修复失败: {str(e)}")
        return False

def fix_import_route():
    """修复导入路由"""
    print("2. 检查导入路由...")
    
    try:
        # 检查路由文件
        routes_dir = os.path.join(os.path.dirname(__file__), 'routes')
        testplan_route = os.path.join(routes_dir, 'testplan.py')
        
        if os.path.exists(testplan_route):
            print("   ✅ testplan.py路由文件存在")
            
            # 检查导入路由是否存在
            with open(testplan_route, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if "@testplan_bp.route('/import', methods=['POST'])" in content:
                print("   ✅ 导入路由已定义")
            else:
                print("   ❌ 导入路由未找到")
                return False
                
            if "def import_excel():" in content:
                print("   ✅ 导入函数已定义")
            else:
                print("   ❌ 导入函数未找到")
                return False
                
        else:
            print(f"   ❌ 路由文件不存在: {testplan_route}")
            return False
        
        print("   ✅ 导入路由检查完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 导入路由检查失败: {str(e)}")
        return False

def test_excel_parser():
    """测试Excel解析器"""
    print("3. 测试Excel解析器...")
    
    try:
        # 切换到正确目录
        dashboard_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(dashboard_dir)
        
        # 导入解析器
        sys.path.insert(0, dashboard_dir)
        from utils.excel_parser import TestPlanParser
        
        print("   ✅ Excel解析器导入成功")
        
        # 检查TestPlan模板
        template_path = os.path.join(dashboard_dir, '..', '..', '..', 'TestPlan_Template.xlsx')
        template_path = os.path.abspath(template_path)
        
        if os.path.exists(template_path):
            print(f"   ✅ 找到TestPlan模板: {template_path}")
            
            # 测试解析
            parser = TestPlanParser()
            test_cases, parse_info = parser.parse_file(template_path, 'TP')
            
            print(f"   ✅ 解析成功，共解析 {len(test_cases)} 条用例")
            
            if len(test_cases) > 0:
                first_case = test_cases[0]
                print(f"   📝 示例用例: {first_case.get('case_name', 'N/A')}")
                print(f"   📝 测试区域: {first_case.get('test_areas', 'N/A')}")
                print(f"   📝 覆盖: {first_case.get('cover', 'N/A')}")
            
        else:
            print(f"   ⚠️ TestPlan模板不存在: {template_path}")
            print("   请确保已生成TestPlan模板文件")
        
        print("   ✅ Excel解析器测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ Excel解析器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_test_service():
    """创建测试服务脚本"""
    print("4. 创建测试服务脚本...")
    
    try:
        dashboard_dir = os.path.dirname(os.path.abspath(__file__))
        test_service_path = os.path.join(dashboard_dir, 'test_service.py')
        
        test_service_content = '''#!/usr/bin/env python3
"""
测试仪表盘服务
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from app import create_app
    
    print("创建Flask应用...")
    app = create_app()
    
    print("启动测试服务器...")
    print("访问地址: http://127.0.0.1:5000")
    print("用例管理: http://127.0.0.1:5000/testplan")
    print("按 Ctrl+C 停止服务")
    
    app.run(host='127.0.0.1', port=5000, debug=True)
    
except Exception as e:
    print(f"启动失败: {str(e)}")
    import traceback
    traceback.print_exc()
'''
        
        with open(test_service_path, 'w', encoding='utf-8') as f:
            f.write(test_service_content)
        
        print(f"   ✅ 测试服务脚本创建成功: {test_service_path}")
        return True
        
    except Exception as e:
        print(f"   ❌ 创建测试服务脚本失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("TestPlan导入问题修复工具")
    print("=" * 40)
    print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 修复步骤
    steps = [
        ("修复数据库结构", fix_database_structure),
        ("检查导入路由", fix_import_route),
        ("测试Excel解析器", test_excel_parser),
        ("创建测试服务脚本", create_test_service)
    ]
    
    results = []
    
    for step_name, step_func in steps:
        try:
            result = step_func()
            results.append((step_name, result))
            print()
        except Exception as e:
            print(f"   ❌ {step_name}执行异常: {str(e)}")
            results.append((step_name, False))
            print()
    
    # 输出修复结果
    print("=" * 40)
    print("修复结果汇总:")
    
    passed = 0
    for step_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {step_name}: {status}")
        if result:
            passed += 1
    
    print(f"\\n总体结果: {passed}/{len(results)} 项成功")
    
    # 给出后续建议
    if passed == len(results):
        print("\\n🎉 修复完成！现在可以:")
        print("  1. 运行测试服务: python plugins/builtin/dashboard_web/test_service.py")
        print("  2. 或重新启动RunSim GUI")
        print("  3. 访问 http://127.0.0.1:5000/testplan 测试导入功能")
    else:
        print("\\n🔧 部分修复失败，建议:")
        print("  1. 检查Python环境和依赖库")
        print("  2. 确保有足够的文件权限")
        print("  3. 重新运行此修复脚本")

if __name__ == "__main__":
    main()
