#!/usr/bin/env python3
"""
Dashboard Web 配置文件

包含仪表板Web应用的所有配置项
"""

import os
from datetime import timedelta

# 获取当前文件所在目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

class Config:
    """基础配置类"""

    # 基础目录
    BASE_DIR = BASE_DIR

    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'runsim-dashboard-secret-key-2024'

    # 数据库配置
    DATABASE_PATH = os.path.join(BASE_DIR, 'data', 'dashboard.db')
    DATABASE_BACKUP_DIR = os.path.join(BASE_DIR, 'data')

    # 上传文件配置
    UPLOAD_FOLDER = os.path.join(BASE_DIR, 'static', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'xlsx', 'xls', 'csv'}

    # 导出文件配置
    EXPORT_FOLDER = os.path.join(BASE_DIR, 'data', 'exports')

    # 缓存配置
    CACHE_DIR = os.path.join(BASE_DIR, 'cache')
    CACHE_TIMEOUT = 300  # 5分钟

    # 分页配置
    CASES_PER_PAGE = 50
    BUGS_PER_PAGE = 20

    # 实时更新配置
    REALTIME_UPDATE_INTERVAL = 30  # 秒
    WEBSOCKET_ENABLED = True

    # 性能优化配置
    ENABLE_GZIP = True
    ENABLE_CACHE = True
    ENABLE_MINIFY = False  # 开发环境关闭

    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = os.path.join(BASE_DIR, 'logs', 'dashboard.log')
    LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5

    # 安全配置
    CSRF_ENABLED = True
    SESSION_TIMEOUT = timedelta(hours=24)

    # API配置
    API_RATE_LIMIT = "1000 per hour"
    API_TIMEOUT = 30  # 秒

    # 图表配置
    CHART_COLORS = {
        'primary': '#007bff',
        'success': '#28a745',
        'danger': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8',
        'secondary': '#6c757d'
    }

    # 验证阶段配置
    VERIFICATION_PHASES = ['DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2']

    # 用例状态配置
    CASE_STATUSES = ['PASS', 'Pending', 'On-Going', 'N/A']

    # BUG状态配置
    BUG_STATUSES = ['Open', 'In Progress', 'Fixed', 'Closed', 'Rejected']

    # 优先级配置
    BUG_PRIORITIES = ['Low', 'Medium', 'High', 'Critical']

    # 严重程度配置
    BUG_SEVERITIES = ['Minor', 'Major', 'Critical', 'Blocker']

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False

    # 开发环境特定配置
    REALTIME_UPDATE_INTERVAL = 10  # 更频繁的更新
    LOG_LEVEL = 'DEBUG'
    ENABLE_MINIFY = False

    # 开发数据库
    DATABASE_PATH = os.path.join(Config.BASE_DIR, 'data', 'dashboard_dev.db')

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False

    # 生产环境特定配置
    ENABLE_MINIFY = True
    LOG_LEVEL = 'WARNING'

    # 更严格的安全配置
    SESSION_TIMEOUT = timedelta(hours=8)
    API_RATE_LIMIT = "500 per hour"

class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    TESTING = True

    # 测试数据库
    DATABASE_PATH = ':memory:'  # 内存数据库

    # 测试特定配置
    CSRF_ENABLED = False
    REALTIME_UPDATE_INTERVAL = 1
    CACHE_TIMEOUT = 1

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """
    获取配置对象

    Args:
        config_name: 配置名称 ('development', 'production', 'testing')

    Returns:
        Config: 配置对象
    """
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')

    return config.get(config_name, config['default'])

# 创建必要的目录
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        Config.UPLOAD_FOLDER,
        Config.EXPORT_FOLDER,
        Config.CACHE_DIR,
        os.path.dirname(Config.LOG_FILE),
        Config.DATABASE_BACKUP_DIR
    ]

    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

# 验证配置
def validate_config(config_obj):
    """
    验证配置的有效性

    Args:
        config_obj: 配置对象

    Returns:
        bool: 配置是否有效
    """
    try:
        # 检查必要的目录
        ensure_directories()

        # 检查数据库路径
        if config_obj.DATABASE_PATH != ':memory:':
            db_dir = os.path.dirname(config_obj.DATABASE_PATH)
            if not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)

        # 检查上传文件大小限制
        if config_obj.MAX_CONTENT_LENGTH <= 0:
            raise ValueError("MAX_CONTENT_LENGTH must be positive")

        # 检查分页配置
        if config_obj.CASES_PER_PAGE <= 0 or config_obj.BUGS_PER_PAGE <= 0:
            raise ValueError("Page size must be positive")

        return True

    except Exception as e:
        print(f"配置验证失败: {e}")
        return False

# 导出常用配置
__all__ = [
    'Config',
    'DevelopmentConfig',
    'ProductionConfig',
    'TestingConfig',
    'config',
    'get_config',
    'ensure_directories',
    'validate_config'
]
