#!/usr/bin/env python3
"""
测试URL构建修复

验证模板渲染问题是否已解决
"""

import os
import sys
from datetime import datetime

def test_template_rendering():
    """测试模板渲染"""
    print("测试模板渲染修复")
    print("=" * 40)
    
    try:
        # 切换到正确的目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # 添加当前目录到Python路径
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        # 导入Flask应用
        from app import create_app
        
        # 创建应用
        app = create_app()
        print("✓ Flask应用创建成功")
        
        # 测试模板渲染
        with app.app_context():
            from flask import render_template
            
            # 测试base.html
            try:
                # 创建一个简单的测试模板
                test_template = """
                {% extends "base.html" %}
                {% block title %}测试页面{% endblock %}
                {% block content %}
                <div class="alert alert-success">
                    <h4>✅ 模板渲染测试成功</h4>
                    <p>base.html模板现在可以正常渲染了！</p>
                </div>
                {% endblock %}
                """
                
                # 保存测试模板
                test_template_path = os.path.join('templates', 'test_template.html')
                with open(test_template_path, 'w', encoding='utf-8') as f:
                    f.write(test_template)
                
                # 渲染测试模板
                result = render_template('test_template.html')
                print(f"✓ base.html 模板渲染成功 ({len(result)} 字符)")
                
                # 清理测试模板
                os.remove(test_template_path)
                
            except Exception as e:
                print(f"❌ base.html 模板渲染失败: {str(e)}")
                return False
            
            # 测试testplan.html
            try:
                result = render_template('testplan.html')
                print(f"✓ testplan.html 模板渲染成功 ({len(result)} 字符)")
            except Exception as e:
                print(f"❌ testplan.html 模板渲染失败: {str(e)}")
                return False
            
            # 测试其他模板
            templates_to_test = [
                'dashboard.html',
                'bug.html',
                'testplan_simple.html'
            ]
            
            for template_name in templates_to_test:
                try:
                    result = render_template(template_name)
                    print(f"✓ {template_name} 渲染成功 ({len(result)} 字符)")
                except Exception as e:
                    print(f"⚠️ {template_name} 渲染失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_routes_with_client():
    """使用测试客户端测试路由"""
    print("\n测试路由访问")
    print("=" * 40)
    
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试所有主要路由
            routes_to_test = [
                ('/', '主页'),
                ('/testplan', '用例管理'),
                ('/bug', 'BUG管理'),
                ('/health', '健康检查'),
            ]
            
            all_success = True
            
            for route, name in routes_to_test:
                try:
                    response = client.get(route)
                    if response.status_code == 200:
                        print(f"✓ {route} ({name}) - 状态码: {response.status_code}")
                    else:
                        print(f"❌ {route} ({name}) - 状态码: {response.status_code}")
                        all_success = False
                except Exception as e:
                    print(f"❌ {route} ({name}) - 异常: {str(e)}")
                    all_success = False
            
            return all_success
        
    except Exception as e:
        print(f"✗ 路由测试失败: {str(e)}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n测试API端点")
    print("=" * 40)
    
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试API端点
            api_endpoints = [
                ('/api/testplan/statistics', '用例统计'),
                ('/api/testplan/cases?page=1&page_size=5', '用例列表'),
            ]
            
            all_success = True
            
            for endpoint, name in api_endpoints:
                try:
                    response = client.get(endpoint)
                    if response.status_code == 200:
                        data = response.get_json()
                        print(f"✓ {endpoint} ({name}) - 成功: {data.get('success', 'unknown')}")
                    else:
                        print(f"⚠️ {endpoint} ({name}) - 状态码: {response.status_code}")
                        all_success = False
                except Exception as e:
                    print(f"❌ {endpoint} ({name}) - 异常: {str(e)}")
                    all_success = False
            
            return all_success
        
    except Exception as e:
        print(f"✗ API测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("URL构建修复验证测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查是否在正确的目录
    if not os.path.exists('app.py'):
        print("❌ 错误: 请在dashboard_web目录下运行此脚本")
        return False
    
    # 运行测试
    tests = [
        ("模板渲染测试", test_template_rendering),
        ("路由访问测试", test_routes_with_client),
        ("API端点测试", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 40)
    print("修复验证结果")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 URL构建问题修复成功！")
        print("\n✅ 现在可以:")
        print("   1. 重启RunSim GUI")
        print("   2. 点击用例管理按钮")
        print("   3. 应该能正常访问testplan页面")
        print("   4. 页面应该显示完整的用例管理界面")
        
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(0)
