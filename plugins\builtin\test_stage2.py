#!/usr/bin/env python3
"""
第二阶段测试脚本

测试Excel解析和用例管理功能
"""

import os
import sys
import time
import tempfile
from datetime import datetime

def test_excel_parser():
    """测试Excel解析器"""
    print("测试Excel解析器...")

    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)

        try:
            from utils.excel_parser import TestPlanParser

            # 创建解析器实例
            parser = TestPlanParser()
            print("  ✓ Excel解析器创建成功")

            # 测试列映射
            test_mapping = parser.COLUMN_MAPPING
            print(f"  ✓ 列映射定义: {len(test_mapping)} 个映射")

            # 测试状态映射
            test_status = parser.STATUS_MAPPING
            print(f"  ✓ 状态映射定义: {len(test_status)} 个映射")

            return True

        finally:
            os.chdir(original_cwd)

    except Exception as e:
        print(f"  ✗ Excel解析器测试失败: {str(e)}")
        return False

def test_excel_exporter():
    """测试Excel导出器"""
    print("\n测试Excel导出器...")

    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)

        try:
            from utils.excel_exporter import TestPlanExporter

            # 创建导出器实例
            exporter = TestPlanExporter()
            print("  ✓ Excel导出器创建成功")

            # 测试导出列定义
            export_columns = exporter.EXPORT_COLUMNS
            print(f"  ✓ 导出列定义: {len(export_columns)} 列")

            # 测试状态颜色映射
            status_colors = exporter.STATUS_COLORS
            print(f"  ✓ 状态颜色映射: {len(status_colors)} 个颜色")

            # 测试模板导出
            template_path = TestPlanExporter.export_template('xlsx')
            if os.path.exists(template_path):
                print(f"  ✓ 模板导出成功: {os.path.basename(template_path)}")
                os.remove(template_path)  # 清理临时文件
            else:
                print("  ✗ 模板导出失败")
                return False

            return True

        finally:
            os.chdir(original_cwd)

    except Exception as e:
        print(f"  ✗ Excel导出器测试失败: {str(e)}")
        return False

def test_testcase_manager():
    """测试用例管理器"""
    print("\n测试用例管理器...")

    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)

        try:
            from models.testplan import TestCaseManager
            from app import create_app

            print("  ✓ 用例管理器导入成功")

            # 创建Flask应用上下文
            test_config = {
                'DATABASE_PATH': os.path.join('data', 'test_stage2.db'),
                'TESTING': True
            }

            app = create_app(test_config)

            with app.app_context():
                # 测试统计功能
                stats = TestCaseManager.get_case_statistics()
                print(f"  ✓ 统计功能正常: {type(stats)}")

                # 测试用例查询
                cases, total = TestCaseManager.get_test_cases(page=1, page_size=10)
                print(f"  ✓ 用例查询功能正常: 查询到 {len(cases)} 条用例，总计 {total} 条")

            return True

        finally:
            os.chdir(original_cwd)

    except Exception as e:
        print(f"  ✗ 用例管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_routes():
    """测试API路由"""
    print("\n测试API路由...")

    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)

        try:
            from routes.testplan import testplan_bp
            from app import create_app

            print("  ✓ 用例管理API蓝图导入成功")

            # 创建临时应用来检查路由
            test_config = {
                'DATABASE_PATH': os.path.join('data', 'test_stage2.db'),
                'TESTING': True
            }

            app = create_app(test_config)

            # 检查路由规则
            with app.app_context():
                rules = []
                for rule in app.url_map.iter_rules():
                    if '/api/testplan' in rule.rule:
                        rules.append(f"{list(rule.methods)} {rule.rule}")

                print(f"  ✓ API路由定义: {len(rules)} 个路由")
                for rule in rules[:5]:  # 显示前5个路由
                    print(f"    - {rule}")

            return True

        finally:
            os.chdir(original_cwd)

    except Exception as e:
        print(f"  ✗ API路由测试失败: {str(e)}")
        return False

def test_flask_integration():
    """测试Flask集成"""
    print("\n测试Flask集成...")

    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)

        try:
            from app import create_app

            # 创建测试应用
            test_config = {
                'DATABASE_PATH': os.path.join('data', 'test_stage2.db'),
                'TESTING': True
            }

            app = create_app(test_config)
            print("  ✓ Flask应用创建成功")

            # 检查蓝图注册
            blueprints = list(app.blueprints.keys())
            print(f"  ✓ 注册的蓝图: {blueprints}")

            # 检查路由
            with app.app_context():
                routes = []
                for rule in app.url_map.iter_rules():
                    if '/api/testplan' in rule.rule:
                        routes.append(rule.rule)

                print(f"  ✓ 用例管理路由: {len(routes)} 个")
                for route in routes[:3]:  # 显示前3个路由
                    print(f"    - {route}")

            return True

        finally:
            os.chdir(original_cwd)

    except Exception as e:
        print(f"  ✗ Flask集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_template_page():
    """测试模板页面"""
    print("\n测试模板页面...")

    try:
        # 检查模板文件
        template_path = os.path.join(os.path.dirname(__file__), 'dashboard_web', 'templates', 'testplan.html')

        if not os.path.exists(template_path):
            print("  ✗ 模板文件不存在")
            return False

        # 读取模板内容
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查关键元素
        required_elements = [
            'id="importModal"',  # 导入模态框
            'id="statusModal"',  # 状态更新模态框
            'id="cases-table-body"',  # 用例表格
            'function loadTestCases',  # JavaScript函数
            'function importExcel',  # 导入函数
        ]

        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)

        if missing_elements:
            print(f"  ✗ 模板缺少元素: {missing_elements}")
            return False

        print(f"  ✓ 模板文件检查通过: {len(content)} 字符")
        print(f"  ✓ 包含所有必要元素: {len(required_elements)} 个")

        return True

    except Exception as e:
        print(f"  ✗ 模板页面测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("RunSim Dashboard 第二阶段测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("阶段目标: Excel解析和用例管理")

    # 检查是否在正确的目录
    if not os.path.exists('plugins/builtin/dashboard_plugin.py'):
        print("❌ 错误: 请在RunSim项目根目录下运行此脚本")
        return False

    # 测试步骤
    tests = [
        ("Excel解析器", test_excel_parser),
        ("Excel导出器", test_excel_exporter),
        ("用例管理器", test_testcase_manager),
        ("API路由", test_api_routes),
        ("Flask集成", test_flask_integration),
        ("模板页面", test_template_page),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ✗ {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))

    # 输出测试结果
    print("\n" + "=" * 50)
    print("第二阶段测试结果汇总")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("\n🎉 第二阶段Excel解析和用例管理功能开发完成！")
        print("\n✅ 已完成的功能:")
        print("   - Excel文件解析器（支持多种格式）")
        print("   - Excel文件导出器（带样式和模板）")
        print("   - 用例数据模型和管理器")
        print("   - 完整的REST API接口")
        print("   - 用例管理Web界面")
        print("   - 文件上传和下载功能")

        print("\n🚀 现在可以:")
        print("   1. 在RunSim GUI中访问用例管理页面")
        print("   2. 导入Excel格式的TestPlan文件")
        print("   3. 在线管理和更新用例状态")
        print("   4. 导出用例数据为Excel文件")
        print("   5. 下载用例模板文件")

        print("\n⏭️  下一步: 开始第三阶段 - 与RunSim GUI集成")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(0)
