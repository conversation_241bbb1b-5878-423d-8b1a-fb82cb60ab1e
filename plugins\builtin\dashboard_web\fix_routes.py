#!/usr/bin/env python3
"""
修复路由问题

确保所有路由都正确注册并可以访问
"""

import os
import sys
from datetime import datetime

def fix_routes():
    """修复路由问题"""
    print("修复Flask路由问题")
    print("=" * 40)
    
    try:
        # 切换到正确的目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # 添加当前目录到Python路径
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        # 导入Flask应用
        from app import create_app
        
        # 创建应用
        app = create_app()
        print("✓ Flask应用创建成功")
        
        # 测试应用
        with app.app_context():
            client = app.test_client()
            
            # 测试所有主要路由
            routes_to_test = [
                ('/', '主页'),
                ('/testplan', '用例管理'),
                ('/bug', 'BUG管理'),
                ('/health', '健康检查'),
            ]
            
            print("\n测试路由:")
            all_ok = True
            
            for route, name in routes_to_test:
                try:
                    response = client.get(route)
                    if response.status_code == 200:
                        print(f"  ✓ {route} ({name}) - 状态码: {response.status_code}")
                    else:
                        print(f"  ✗ {route} ({name}) - 状态码: {response.status_code}")
                        all_ok = False
                except Exception as e:
                    print(f"  ✗ {route} ({name}) - 错误: {str(e)}")
                    all_ok = False
            
            # 列出所有注册的路由
            print("\n所有注册的路由:")
            for rule in app.url_map.iter_rules():
                methods = ', '.join(rule.methods - {'HEAD', 'OPTIONS'})
                print(f"  {methods:10} {rule.rule}")
            
            if all_ok:
                print("\n✅ 所有路由测试通过")
                
                # 启动开发服务器进行实际测试
                print("\n启动开发服务器进行实际测试...")
                print("服务器将在 http://127.0.0.1:5000 启动")
                print("请在浏览器中访问 http://127.0.0.1:5000/testplan")
                print("按 Ctrl+C 停止服务器")
                
                try:
                    app.run(host='127.0.0.1', port=5000, debug=True)
                except KeyboardInterrupt:
                    print("\n服务器已停止")
                
                return True
            else:
                print("\n❌ 部分路由测试失败")
                return False
        
    except Exception as e:
        print(f"✗ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_test_server():
    """创建一个简单的测试服务器"""
    print("\n创建简单测试服务器")
    print("=" * 40)
    
    try:
        from flask import Flask, render_template
        
        app = Flask(__name__)
        
        @app.route('/')
        def index():
            return render_template('dashboard.html')
        
        @app.route('/testplan')
        def testplan():
            return render_template('testplan.html')
        
        @app.route('/bug')
        def bug():
            return render_template('bug.html')
        
        @app.route('/health')
        def health():
            return {'status': 'ok', 'message': '简单测试服务器运行正常'}
        
        @app.errorhandler(404)
        def not_found(error):
            return render_template('error.html', 
                                 error_code=404, 
                                 error_message='页面不存在'), 404
        
        print("✓ 简单测试服务器创建成功")
        print("启动服务器在 http://127.0.0.1:5001")
        print("请在浏览器中访问 http://127.0.0.1:5001/testplan")
        print("按 Ctrl+C 停止服务器")
        
        try:
            app.run(host='127.0.0.1', port=5001, debug=True)
        except KeyboardInterrupt:
            print("\n简单测试服务器已停止")
        
        return True
        
    except Exception as e:
        print(f"✗ 创建简单测试服务器失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("Flask路由修复工具")
    print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查是否在正确的目录
    if not os.path.exists('app.py'):
        print("❌ 错误: 请在dashboard_web目录下运行此脚本")
        return False
    
    print("\n选择修复方式:")
    print("1. 测试完整Flask应用")
    print("2. 创建简单测试服务器")
    
    try:
        choice = input("\n请选择 (1/2): ").strip()
        
        if choice == '1':
            return fix_routes()
        elif choice == '2':
            return create_simple_test_server()
        else:
            print("无效选择，默认使用完整Flask应用测试")
            return fix_routes()
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n修复被用户中断")
        sys.exit(0)
