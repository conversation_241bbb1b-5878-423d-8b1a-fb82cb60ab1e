#!/usr/bin/env python3
"""
BUG管理系统测试脚本

该脚本用于测试BUG管理系统的各项功能，包括：
- 数据库连接和表结构
- BUG模型的CRUD操作
- API接口的响应
- 前端页面的加载
"""

import os
import sys
import json
import sqlite3
from datetime import datetime, date

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_database_connection():
    """测试数据库连接和表结构"""
    print("=== 测试数据库连接 ===")
    
    try:
        from models.database import init_database, get_db_manager
        
        db_path = os.path.join(current_dir, 'data', 'test_bug_system.db')
        
        # 初始化数据库
        if init_database(db_path):
            print("✓ 数据库初始化成功")
        else:
            print("✗ 数据库初始化失败")
            return False
        
        # 检查表结构
        db_manager = get_db_manager(db_path)
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查bugs表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bugs'")
            if cursor.fetchone():
                print("✓ bugs表存在")
                
                # 检查表结构
                cursor.execute("PRAGMA table_info(bugs)")
                columns = [row[1] for row in cursor.fetchall()]
                expected_columns = [
                    'id', 'project_id', 'bug_id', 'bug_type', 'submit_sys',
                    'verification_stage', 'description', 'discovery_platform',
                    'discovery_case', 'severity', 'status', 'submitter',
                    'verifier', 'submit_date', 'fix_date', 'created_at', 'updated_at'
                ]
                
                missing_columns = [col for col in expected_columns if col not in columns]
                if not missing_columns:
                    print("✓ bugs表结构正确")
                else:
                    print(f"✗ bugs表缺少字段: {missing_columns}")
                    return False
            else:
                print("✗ bugs表不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")
        return False

def test_bug_model():
    """测试BUG模型功能"""
    print("\n=== 测试BUG模型 ===")
    
    try:
        from models.bug import BugModel
        
        db_path = os.path.join(current_dir, 'data', 'test_bug_system.db')
        bug_model = BugModel(db_path)
        
        # 测试创建BUG
        test_bug_data = {
            'bug_id': 'TEST-001',
            'bug_type': '功能缺陷',
            'description': '这是一个测试BUG',
            'severity': 'Medium',
            'status': 'Open',
            'submitter': '测试用户',
            'submit_date': date.today().isoformat()
        }
        
        bug_id = bug_model.create_bug(test_bug_data)
        if bug_id:
            print(f"✓ 创建BUG成功，ID: {bug_id}")
        else:
            print("✗ 创建BUG失败")
            return False
        
        # 测试获取BUG
        bug = bug_model.get_bug_by_id(bug_id)
        if bug and bug['bug_id'] == 'TEST-001':
            print("✓ 获取BUG成功")
        else:
            print("✗ 获取BUG失败")
            return False
        
        # 测试更新BUG
        update_data = {'status': 'Fixed', 'fix_date': date.today().isoformat()}
        if bug_model.update_bug(bug_id, update_data):
            print("✓ 更新BUG成功")
        else:
            print("✗ 更新BUG失败")
            return False
        
        # 测试获取BUG列表
        bugs, total = bug_model.get_bugs_list()
        if total > 0:
            print(f"✓ 获取BUG列表成功，共{total}条记录")
        else:
            print("✗ 获取BUG列表失败")
            return False
        
        # 测试统计功能
        stats = bug_model.get_bug_statistics()
        if stats['total_bugs'] > 0:
            print(f"✓ 获取BUG统计成功，总数: {stats['total_bugs']}")
        else:
            print("✗ 获取BUG统计失败")
            return False
        
        # 测试趋势数据
        trend = bug_model.get_bug_trend_data(7)  # 7天数据
        if 'labels' in trend and 'new_bugs' in trend:
            print("✓ 获取BUG趋势数据成功")
        else:
            print("✗ 获取BUG趋势数据失败")
            return False
        
        # 测试删除BUG
        if bug_model.delete_bug(bug_id):
            print("✓ 删除BUG成功")
        else:
            print("✗ 删除BUG失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ BUG模型测试失败: {e}")
        return False

def test_flask_app():
    """测试Flask应用"""
    print("\n=== 测试Flask应用 ===")
    
    try:
        from app import create_app
        
        # 创建测试配置
        test_config = {
            'DATABASE_PATH': os.path.join(current_dir, 'data', 'test_bug_system.db'),
            'TESTING': True
        }
        
        app = create_app(test_config)
        
        with app.test_client() as client:
            # 测试健康检查
            response = client.get('/health')
            if response.status_code == 200:
                print("✓ 健康检查API正常")
            else:
                print(f"✗ 健康检查API失败: {response.status_code}")
                return False
            
            # 测试BUG页面
            response = client.get('/bug')
            if response.status_code == 200:
                print("✓ BUG管理页面加载正常")
            else:
                print(f"✗ BUG管理页面加载失败: {response.status_code}")
                return False
            
            # 测试BUG API
            response = client.get('/api/bugs')
            if response.status_code == 200:
                print("✓ BUG列表API正常")
            else:
                print(f"✗ BUG列表API失败: {response.status_code}")
                return False
            
            # 测试BUG统计API
            response = client.get('/api/bugs/statistics')
            if response.status_code == 200:
                print("✓ BUG统计API正常")
            else:
                print(f"✗ BUG统计API失败: {response.status_code}")
                return False
            
            # 测试BUG趋势API
            response = client.get('/api/bugs/trend')
            if response.status_code == 200:
                print("✓ BUG趋势API正常")
            else:
                print(f"✗ BUG趋势API失败: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Flask应用测试失败: {e}")
        return False

def test_api_integration():
    """测试API集成"""
    print("\n=== 测试API集成 ===")
    
    try:
        from app import create_app
        
        test_config = {
            'DATABASE_PATH': os.path.join(current_dir, 'data', 'test_bug_system.db'),
            'TESTING': True
        }
        
        app = create_app(test_config)
        
        with app.test_client() as client:
            # 创建测试BUG
            test_bug = {
                'bug_id': 'API-TEST-001',
                'description': 'API测试BUG',
                'severity': 'High',
                'status': 'Open',
                'submitter': 'API测试'
            }
            
            response = client.post('/api/bugs',
                                 data=json.dumps(test_bug),
                                 content_type='application/json')
            
            if response.status_code == 200:
                data = response.get_json()
                if data.get('success'):
                    bug_id = data['data']['id']
                    print(f"✓ API创建BUG成功，ID: {bug_id}")
                    
                    # 测试获取BUG
                    response = client.get(f'/api/bugs/{bug_id}')
                    if response.status_code == 200:
                        print("✓ API获取BUG成功")
                    else:
                        print("✗ API获取BUG失败")
                        return False
                    
                    # 测试更新BUG
                    update_data = {'status': 'Fixed'}
                    response = client.put(f'/api/bugs/{bug_id}',
                                        data=json.dumps(update_data),
                                        content_type='application/json')
                    if response.status_code == 200:
                        print("✓ API更新BUG成功")
                    else:
                        print("✗ API更新BUG失败")
                        return False
                    
                    # 测试删除BUG
                    response = client.delete(f'/api/bugs/{bug_id}')
                    if response.status_code == 200:
                        print("✓ API删除BUG成功")
                    else:
                        print("✗ API删除BUG失败")
                        return False
                else:
                    print(f"✗ API创建BUG失败: {data.get('message')}")
                    return False
            else:
                print(f"✗ API创建BUG请求失败: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ API集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始BUG管理系统测试...")
    print("=" * 50)
    
    tests = [
        test_database_connection,
        test_bug_model,
        test_flask_app,
        test_api_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"\n测试失败，停止后续测试")
            break
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！BUG管理系统可以正常使用。")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
