#!/usr/bin/env python
"""
修复API一致性问题
"""

import os
import sys
import sqlite3

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def fix_api_consistency():
    """修复API一致性问题"""
    print("🔧 修复API一致性问题")
    print("=" * 40)
    
    # 数据库路径
    db_path = os.path.join(current_dir, 'data', 'dashboard.db')
    print(f"📁 数据库路径: {db_path}")
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 1. 检查数据库结构
            print("\n1. 检查数据库结构:")
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row['name'] for row in cursor.fetchall()]
            print(f"   表: {tables}")
            
            # 2. 检查用例数据
            print("\n2. 检查用例数据:")
            cursor.execute("SELECT COUNT(*) as count FROM test_cases")
            total_count = cursor.fetchone()['count']
            print(f"   总用例数: {total_count}")
            
            if total_count == 0:
                print("   ⚠️ 数据库中没有用例数据")
                return False
            
            # 3. 检查项目数据
            print("\n3. 检查项目数据:")
            cursor.execute("SELECT COUNT(*) as count FROM projects")
            project_count = cursor.fetchone()['count']
            print(f"   项目数: {project_count}")
            
            # 4. 模拟仪表盘统计API查询
            print("\n4. 模拟仪表盘统计API查询:")
            cursor.execute('''
                SELECT
                    COUNT(*) as total_cases,
                    SUM(CASE WHEN subsys_status = 'Pass' OR top_status = 'Pass'
                             OR post_subsys_status = 'Pass' OR post_top_status = 'Pass'
                        THEN 1 ELSE 0 END) as passed_cases,
                    SUM(CASE WHEN subsys_status = 'Fail' OR top_status = 'Fail'
                             OR post_subsys_status = 'Fail' OR post_top_status = 'Fail'
                        THEN 1 ELSE 0 END) as failed_cases,
                    SUM(CASE WHEN subsys_status = 'On-Going' OR top_status = 'On-Going'
                             OR post_subsys_status = 'On-Going' OR post_top_status = 'On-Going'
                        THEN 1 ELSE 0 END) as running_cases
                FROM test_cases
            ''')
            dashboard_stats = cursor.fetchone()
            print(f"   仪表盘统计:")
            print(f"      总用例: {dashboard_stats['total_cases']}")
            print(f"      通过: {dashboard_stats['passed_cases']}")
            print(f"      失败: {dashboard_stats['failed_cases']}")
            print(f"      进行中: {dashboard_stats['running_cases']}")
            
            # 5. 模拟用例列表API查询
            print("\n5. 模拟用例列表API查询:")
            where_clause = "1=1"
            params = []
            
            count_query = f"""
                SELECT COUNT(*) as total
                FROM test_cases tc
                LEFT JOIN projects p ON tc.project_id = p.id
                WHERE {where_clause}
            """
            cursor.execute(count_query, params)
            list_total = cursor.fetchone()['total']
            print(f"   用例列表总数: {list_total}")
            
            data_query = f"""
                SELECT
                    tc.id, tc.case_name, tc.subsys_status, tc.top_status,
                    p.name as project_name
                FROM test_cases tc
                LEFT JOIN projects p ON tc.project_id = p.id
                WHERE {where_clause}
                ORDER BY tc.created_at DESC
                LIMIT 5
            """
            cursor.execute(data_query, params)
            list_cases = cursor.fetchall()
            print(f"   用例列表返回: {len(list_cases)} 条")
            
            if list_cases:
                print("   示例用例:")
                for case in list_cases:
                    print(f"      {case['case_name']} - {case['subsys_status']} (项目: {case['project_name']})")
            
            # 6. 检查数据一致性
            print("\n6. 检查数据一致性:")
            if dashboard_stats['total_cases'] == list_total:
                print("   ✅ 统计数据一致")
            else:
                print(f"   ❌ 统计数据不一致: 仪表盘={dashboard_stats['total_cases']}, 列表={list_total}")
            
            # 7. 检查状态分布
            print("\n7. 检查状态分布:")
            cursor.execute("""
                SELECT 
                    subsys_status,
                    COUNT(*) as count
                FROM test_cases 
                WHERE subsys_status IS NOT NULL AND subsys_status != ''
                GROUP BY subsys_status
            """)
            status_dist = cursor.fetchall()
            print("   Subsys状态分布:")
            for status in status_dist:
                print(f"      {status['subsys_status']}: {status['count']}")
            
            # 8. 检查是否有空状态
            print("\n8. 检查空状态:")
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM test_cases 
                WHERE (subsys_status IS NULL OR subsys_status = '')
                AND (top_status IS NULL OR top_status = '')
                AND (post_subsys_status IS NULL OR post_subsys_status = '')
                AND (post_top_status IS NULL OR post_top_status = '')
            """)
            empty_status_count = cursor.fetchone()['count']
            print(f"   空状态用例数: {empty_status_count}")
            
            if empty_status_count > 0:
                print("   🔧 修复空状态用例...")
                cursor.execute("""
                    UPDATE test_cases 
                    SET subsys_status = 'Not Started',
                        top_status = 'Not Started',
                        post_subsys_status = 'Not Started',
                        post_top_status = 'Not Started'
                    WHERE (subsys_status IS NULL OR subsys_status = '')
                    AND (top_status IS NULL OR top_status = '')
                    AND (post_subsys_status IS NULL OR post_subsys_status = '')
                    AND (post_top_status IS NULL OR post_top_status = '')
                """)
                conn.commit()
                print(f"   ✅ 修复了 {cursor.rowcount} 个空状态用例")
            
            print("\n🎉 API一致性检查完成!")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = fix_api_consistency()
    if success:
        print("\n✅ 修复完成!")
        print("现在可以刷新页面查看效果")
    else:
        print("\n❌ 修复失败!")
        print("需要进一步检查问题")
