#!/usr/bin/env python
"""
修复重复导入问题的脚本
"""

import os
import sys
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def main():
    print("🔧 修复重复导入问题")
    print("=" * 50)

    try:
        from app import create_app
        app = create_app()

        with app.app_context():
            fix_duplicate_issue()

    except Exception as e:
        print(f"❌ 失败: {e}")
        traceback.print_exc()

def fix_duplicate_issue():
    """修复重复导入问题"""

    print("1. 检查现有用例...")
    from models.database import get_db

    with get_db() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT case_name FROM test_cases ORDER BY case_name")
        existing_cases = [row[0] for row in cursor.fetchall()]
        print(f"   📊 现有用例数: {len(existing_cases)}")

        # 显示部分现有用例
        if existing_cases:
            print("   📝 现有用例示例:")
            for case in existing_cases[:5]:
                print(f"      - {case}")
            if len(existing_cases) > 5:
                print(f"      ... 还有 {len(existing_cases) - 5} 个用例")

    print("\n2. 解析TestPlan模板...")
    template_path = os.path.join(current_dir, '..', '..', '..', 'TestPlan_Template.xlsx')
    template_path = os.path.abspath(template_path)

    from utils.excel_parser import TestPlanParser
    parser = TestPlanParser()
    test_cases, parse_info = parser.parse_file(template_path, 'TP')

    template_case_names = [case.get('case_name') for case in test_cases if case.get('case_name')]
    print(f"   📊 模板用例数: {len(template_case_names)}")

    # 检查重复
    duplicates = [name for name in template_case_names if name in existing_cases]
    new_cases = [case for case in test_cases if case.get('case_name') not in existing_cases]

    print(f"   🔄 重复用例: {len(duplicates)} 个")
    print(f"   ✨ 新用例: {len(new_cases)} 个")

    if duplicates:
        print("   📝 重复用例列表:")
        for dup in duplicates[:5]:
            print(f"      - {dup}")
        if len(duplicates) > 5:
            print(f"      ... 还有 {len(duplicates) - 5} 个")

    print("\n3. 提供解决方案...")

    print("   方案A: 跳过重复用例，只导入新用例")
    if new_cases:
        print(f"      可导入 {len(new_cases)} 个新用例")

        choice = input("   是否执行方案A? (y/n): ").lower().strip()
        if choice == 'y':
            try:
                from models.testplan import TestCaseManager
                success_count, import_errors = TestCaseManager.batch_import_cases(new_cases, 1)
                print(f"   ✅ 导入结果: 成功 {success_count}, 失败 {len(import_errors)}")

                if import_errors:
                    print("   ❌ 错误:")
                    for error in import_errors[:3]:
                        print(f"      {error}")

            except Exception as e:
                print(f"   ❌ 导入失败: {e}")
    else:
        print("      没有新用例可导入")

    print("\n   方案B: 更新现有用例")
    print("      将用更新模式替换现有用例数据")

    choice = input("   是否执行方案B? (y/n): ").lower().strip()
    if choice == 'y':
        try:
            update_existing_cases(test_cases, existing_cases)
        except Exception as e:
            print(f"   ❌ 更新失败: {e}")
            traceback.print_exc()

    print("\n   方案C: 清空并重新导入")
    print("      ⚠️ 警告: 这将删除所有现有用例数据")

    choice = input("   是否执行方案C? (y/n): ").lower().strip()
    if choice == 'y':
        confirm = input("   确认删除所有用例? 输入 'DELETE' 确认: ").strip()
        if confirm == 'DELETE':
            try:
                clear_and_reimport(test_cases)
            except Exception as e:
                print(f"   ❌ 清空重导失败: {e}")
                traceback.print_exc()
        else:
            print("   取消操作")

def update_existing_cases(test_cases, existing_cases):
    """更新现有用例"""
    print("   🔄 开始更新现有用例...")

    from models.database import get_db

    updated_count = 0
    error_count = 0

    with get_db() as conn:
        cursor = conn.cursor()

        for case in test_cases:
            case_name = case.get('case_name')
            if case_name in existing_cases:
                try:
                    # 更新用例
                    cursor.execute('''
                        UPDATE test_cases SET
                            test_areas = ?, test_scope = ?, cover = ?,
                            category = ?, number = ?, function_point = ?,
                            check_point = ?, subsys_status = ?, top_status = ?,
                            post_subsys_status = ?, post_top_status = ?
                        WHERE case_name = ?
                    ''', (
                        case.get('test_areas', ''),
                        case.get('test_scope', ''),
                        case.get('cover', ''),
                        case.get('category', ''),
                        case.get('number', ''),
                        case.get('function_point', ''),
                        case.get('check_point', ''),
                        case.get('subsys_status', 'Not Started'),
                        case.get('top_status', 'Not Started'),
                        case.get('post_subsys_status', 'Not Started'),
                        case.get('post_top_status', 'Not Started'),
                        case_name
                    ))
                    updated_count += 1

                except Exception as e:
                    print(f"      ❌ 更新失败 {case_name}: {e}")
                    error_count += 1

        conn.commit()

    print(f"   ✅ 更新完成: 成功 {updated_count}, 失败 {error_count}")

def clear_and_reimport(test_cases):
    """清空并重新导入"""
    print("   🗑️ 清空现有用例...")

    from models.database import get_db
    from models.testplan import TestCaseManager
    from utils.excel_parser import TestPlanParser

    with get_db() as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM test_cases")
        conn.commit()
        print("   ✅ 现有用例已清空")

    print("   📥 重新导入用例...")
    valid_cases, validation_errors = TestPlanParser.validate_test_cases(test_cases)
    success_count, import_errors = TestCaseManager.batch_import_cases(valid_cases, 1)

    print(f"   ✅ 导入完成: 成功 {success_count}, 失败 {len(import_errors)}")

    if import_errors:
        print("   ❌ 导入错误:")
        for error in import_errors[:3]:
            print(f"      {error}")

if __name__ == '__main__':
    main()
