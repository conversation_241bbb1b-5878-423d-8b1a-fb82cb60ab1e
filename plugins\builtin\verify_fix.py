#!/usr/bin/env python3
"""
验证修复脚本

验证数据库问题是否已修复
"""

import os
import sys
import sqlite3
from datetime import datetime

def verify_database_fix():
    """验证数据库修复"""
    print("验证数据库修复")
    print("=" * 40)
    
    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)
        
        try:
            # 步骤1: 测试数据库模块导入
            print("1. 测试数据库模块导入...")
            from models.database import init_database, DatabaseManager
            print("   ✓ 数据库模块导入成功")
            
            # 步骤2: 测试数据库初始化
            print("2. 测试数据库初始化...")
            test_db = os.path.join('data', 'verify_fix.db')
            os.makedirs(os.path.dirname(test_db), exist_ok=True)
            
            # 删除现有文件
            if os.path.exists(test_db):
                os.remove(test_db)
            
            result = init_database(test_db)
            if result:
                print("   ✓ 数据库初始化成功")
                
                # 检查表
                conn = sqlite3.connect(test_db)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                conn.close()
                
                expected_tables = ['projects', 'test_cases', 'bugs', 'case_status_history', 'system_config']
                missing = [t for t in expected_tables if t not in tables]
                
                if missing:
                    print(f"   ✗ 缺少表: {missing}")
                    return False
                else:
                    print(f"   ✓ 所有表创建成功: {len(tables)} 个表")
            else:
                print("   ✗ 数据库初始化失败")
                return False
            
            # 步骤3: 测试数据生成
            print("3. 测试数据生成...")
            from utils.test_data import generate_test_data
            
            # 使用新的数据库文件
            test_db2 = os.path.join('data', 'verify_data_gen.db')
            if os.path.exists(test_db2):
                os.remove(test_db2)
            
            generate_test_data(test_db2)
            print("   ✓ 测试数据生成成功")
            
            # 验证数据
            conn = sqlite3.connect(test_db2)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM projects")
            project_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM test_cases")
            case_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM bugs")
            bug_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM case_status_history")
            history_count = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"   ✓ 数据验证: 项目={project_count}, 用例={case_count}, BUG={bug_count}, 历史={history_count}")
            
            if project_count > 0 and case_count > 0:
                print("   ✓ 数据生成验证通过")
                return True
            else:
                print("   ✗ 数据生成验证失败")
                return False
            
        finally:
            os.chdir(original_cwd)
        
    except Exception as e:
        print(f"   ✗ 验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_test_framework():
    """验证test_framework.py是否能正常运行"""
    print("\n验证test_framework.py")
    print("=" * 40)
    
    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)
        
        try:
            # 导入并运行部分测试
            from test_framework import test_database, test_models, generate_test_data
            
            print("1. 测试基础数据库功能...")
            if test_database():
                print("   ✓ 基础数据库功能正常")
            else:
                print("   ✗ 基础数据库功能失败")
                return False
            
            print("2. 测试数据模型...")
            if test_models():
                print("   ✓ 数据模型测试通过")
            else:
                print("   ✗ 数据模型测试失败")
                return False
            
            print("3. 测试数据生成...")
            if generate_test_data():
                print("   ✓ 数据生成测试通过")
            else:
                print("   ✗ 数据生成测试失败")
                return False
            
            return True
            
        finally:
            os.chdir(original_cwd)
        
    except Exception as e:
        print(f"   ✗ test_framework验证失败: {str(e)}")
        return False

def main():
    """主验证函数"""
    print("数据库问题修复验证")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 验证步骤
    tests = [
        ("数据库修复验证", verify_database_fix),
        ("test_framework验证", verify_test_framework),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 40)
    print("验证结果汇总")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个验证通过")
    
    if passed == total:
        print("\n🎉 修复验证成功！数据库问题已解决！")
        print("\n✅ 现在可以正常运行:")
        print("   - test_framework.py")
        print("   - 数据库初始化和数据生成")
        print("   - 第一阶段的所有功能")
        
        print("\n🚀 下一步:")
        print("   1. 运行完整的第一阶段测试")
        print("   2. 启动仪表板插件")
        print("   3. 开始第二阶段开发")
        
        return True
    else:
        print("\n⚠️ 部分验证失败，请检查错误信息")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
