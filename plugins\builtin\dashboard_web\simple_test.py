#!/usr/bin/env python3
"""
简单的BUG管理系统验证脚本
"""

import os
import sys

print("开始BUG管理系统验证...")

# 检查文件是否存在
files_to_check = [
    'models/bug.py',
    'routes/bug.py',
    'templates/bug.html',
    'static/js/bug.js'
]

print("\n=== 检查文件存在性 ===")
for file_path in files_to_check:
    if os.path.exists(file_path):
        print(f"✓ {file_path} 存在")
    else:
        print(f"✗ {file_path} 不存在")

# 检查数据库文件
print("\n=== 检查数据库 ===")
db_path = 'data/dashboard.db'
if os.path.exists(db_path):
    print(f"✓ 数据库文件存在: {db_path}")
    
    # 检查数据库表
    import sqlite3
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查bugs表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bugs'")
        if cursor.fetchone():
            print("✓ bugs表存在")
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(bugs)")
            columns = [row[1] for row in cursor.fetchall()]
            print(f"✓ bugs表字段: {', '.join(columns)}")
        else:
            print("✗ bugs表不存在")
        
        conn.close()
    except Exception as e:
        print(f"✗ 数据库检查失败: {e}")
else:
    print(f"✗ 数据库文件不存在: {db_path}")

# 检查模块导入
print("\n=== 检查模块导入 ===")
try:
    sys.path.insert(0, '.')
    from models.bug import BugModel
    print("✓ BugModel导入成功")
except Exception as e:
    print(f"✗ BugModel导入失败: {e}")

try:
    from routes.bug import bug_bp
    print("✓ bug_bp导入成功")
except Exception as e:
    print(f"✗ bug_bp导入失败: {e}")

try:
    from app import create_app
    print("✓ create_app导入成功")
except Exception as e:
    print(f"✗ create_app导入失败: {e}")

print("\n=== 验证完成 ===")
print("如果所有检查都通过，BUG管理系统应该可以正常工作。")
