#!/usr/bin/env python3
"""
测试仪表盘服务
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from app import create_app

    print("创建Flask应用...")
    app = create_app()

    print("启动测试服务器...")
    print("访问地址: http://127.0.0.1:5001")
    print("用例管理: http://127.0.0.1:5001/testplan")
    print("按 Ctrl+C 停止服务")

    app.run(host='127.0.0.1', port=5001, debug=True)

except Exception as e:
    print(f"启动失败: {str(e)}")
    import traceback
    traceback.print_exc()
