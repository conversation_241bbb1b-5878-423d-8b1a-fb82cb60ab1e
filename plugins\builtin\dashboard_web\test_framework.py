#!/usr/bin/env python3
"""
基础框架测试脚本

该脚本用于测试仪表板基础框架的各个组件是否正常工作。
"""

import os
import sys
import sqlite3
from datetime import datetime

def test_database():
    """测试数据库功能"""
    print("=" * 50)
    print("测试数据库功能")
    print("=" * 50)

    try:
        # 测试SQLite基础功能
        db_path = os.path.join('data', 'test_framework.db')

        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # 创建数据库连接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 创建测试表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 插入测试数据
        cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("测试数据",))

        # 查询数据
        cursor.execute("SELECT * FROM test_table")
        results = cursor.fetchall()

        conn.commit()
        conn.close()

        print(f"✓ 数据库创建成功: {db_path}")
        print(f"✓ 数据插入成功，查询结果: {results}")

        return True

    except Exception as e:
        print(f"✗ 数据库测试失败: {str(e)}")
        return False

def test_models():
    """测试数据模型"""
    print("\n" + "=" * 50)
    print("测试数据模型")
    print("=" * 50)

    try:
        # 导入数据库模型
        from models.database import init_database, DatabaseManager

        # 测试数据库初始化
        db_path = os.path.join('data', 'test_models.db')

        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        print(f"正在初始化数据库: {db_path}")
        result = init_database(db_path)

        if result:
            print("✓ 数据库初始化成功")

            # 测试数据库管理器
            db_manager = DatabaseManager(db_path)
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()

            table_names = [t[0] for t in tables]
            print(f"✓ 数据库管理器工作正常，创建的表: {table_names}")

            # 验证必要的表都存在
            required_tables = ['projects', 'test_cases', 'bugs', 'case_status_history', 'system_config']
            missing_tables = [t for t in required_tables if t not in table_names]

            if missing_tables:
                print(f"✗ 缺少必要的表: {missing_tables}")
                return False
            else:
                print("✓ 所有必要的表都已创建")
                return True
        else:
            print("✗ 数据库初始化失败")
            return False

    except Exception as e:
        print(f"✗ 数据模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_flask_import():
    """测试Flask导入"""
    print("\n" + "=" * 50)
    print("测试Flask导入")
    print("=" * 50)

    try:
        import flask
        print(f"✓ Flask导入成功，版本: {flask.__version__}")
        return True
    except ImportError:
        print("✗ Flask未安装，请运行: pip install flask")
        return False
    except Exception as e:
        print(f"✗ Flask导入失败: {str(e)}")
        return False

def test_app_creation():
    """测试Flask应用创建"""
    print("\n" + "=" * 50)
    print("测试Flask应用创建")
    print("=" * 50)

    try:
        from app import create_app

        # 创建测试配置
        test_config = {
            'DATABASE_PATH': os.path.join('data', 'test_app.db'),
            'TESTING': True
        }

        app = create_app(test_config)
        print(f"✓ Flask应用创建成功")
        print(f"✓ 应用配置: {app.config.get('DATABASE_PATH')}")

        # 测试应用上下文
        with app.app_context():
            print("✓ 应用上下文工作正常")

        return True

    except Exception as e:
        print(f"✗ Flask应用创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_plugin_structure():
    """测试插件结构"""
    print("\n" + "=" * 50)
    print("测试插件结构")
    print("=" * 50)

    try:
        # 检查必要的文件是否存在
        required_files = [
            '../dashboard_plugin.py',
            'app.py',
            'models/database.py',
            'routes/api.py',
            'templates/base.html',
            'templates/dashboard.html',
        ]

        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)

        if missing_files:
            print(f"✗ 缺少文件: {missing_files}")
            return False
        else:
            print("✓ 所有必要文件都存在")

        # 检查目录结构
        required_dirs = [
            'data',
            'models',
            'routes',
            'templates',
            'utils',
            'static'
        ]

        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                print(f"✓ 创建目录: {dir_path}")
            else:
                print(f"✓ 目录存在: {dir_path}")

        return True

    except Exception as e:
        print(f"✗ 插件结构测试失败: {str(e)}")
        return False

def generate_test_data():
    """生成测试数据"""
    print("\n" + "=" * 50)
    print("生成测试数据")
    print("=" * 50)

    try:
        # 确保数据库目录存在
        db_path = os.path.join('data', 'dashboard.db')
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # 首先初始化数据库
        from models.database import init_database
        print(f"初始化数据库: {db_path}")
        if not init_database(db_path):
            print("✗ 数据库初始化失败")
            return False

        # 然后生成测试数据
        from utils.test_data import generate_test_data
        generate_test_data(db_path)
        print("✓ 测试数据生成成功")
        return True

    except Exception as e:
        print(f"✗ 测试数据生成失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("RunSim Dashboard 基础框架测试")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

    # 切换到正确的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)

    # 添加当前目录到Python路径
    if script_dir not in sys.path:
        sys.path.insert(0, script_dir)

    # 运行测试（调整顺序，确保数据库先初始化）
    tests = [
        ("基础数据库功能", test_database),
        ("插件结构检查", test_plugin_structure),
        ("Flask导入测试", test_flask_import),
        ("数据模型测试", test_models),
        ("Flask应用创建", test_app_creation),
        ("生成测试数据", generate_test_data),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))

    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("🎉 所有测试通过！基础框架搭建成功！")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息并修复问题。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
