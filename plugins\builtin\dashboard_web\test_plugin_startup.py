#!/usr/bin/env python
"""
测试插件启动
"""

import os
import sys
import threading
import time

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_plugin_startup():
    """测试插件启动"""
    print("🧪 测试插件启动")
    print("=" * 40)
    
    try:
        # 模拟插件启动环境
        print("1. 模拟插件启动环境...")
        
        # 获取路径
        dashboard_path = current_dir
        plugin_dir = os.path.dirname(current_dir)
        
        print(f"   📁 dashboard_path: {dashboard_path}")
        print(f"   📁 plugin_dir: {plugin_dir}")
        
        # 清理并重新设置Python路径
        paths_to_add = [
            dashboard_path,
            os.path.join(dashboard_path, 'models'),
            os.path.join(dashboard_path, 'utils'), 
            os.path.join(dashboard_path, 'routes'),
            plugin_dir
        ]
        
        # 移除旧路径
        for path in paths_to_add:
            while path in sys.path:
                sys.path.remove(path)
        
        # 按优先级添加路径
        for path in reversed(paths_to_add):
            if os.path.exists(path):
                sys.path.insert(0, path)
                print(f"   ✅ 添加路径: {path}")
        
        # 切换工作目录
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        print(f"   📁 工作目录: {os.getcwd()}")
        
        try:
            # 2. 测试关键模块导入
            print("\n2. 测试关键模块导入...")
            
            # 测试数据库模块
            try:
                from models.database import get_db, init_database
                print("   ✅ models.database 导入成功")
            except ImportError as e:
                print(f"   ❌ models.database 导入失败: {e}")
                return False
            
            # 测试工具模块
            try:
                from utils.excel_parser import TestPlanParser
                print("   ✅ utils.excel_parser 导入成功")
            except ImportError as e:
                print(f"   ⚠️ utils.excel_parser 导入失败: {e}")
            
            # 测试路由模块
            try:
                from routes.api import api_bp
                print("   ✅ routes.api 导入成功")
            except ImportError as e:
                print(f"   ❌ routes.api 导入失败: {e}")
                return False
            
            try:
                from routes.testplan import testplan_bp
                print("   ✅ routes.testplan 导入成功")
            except ImportError as e:
                print(f"   ❌ routes.testplan 导入失败: {e}")
                return False
            
            # 3. 测试Flask应用创建
            print("\n3. 测试Flask应用创建...")
            
            try:
                from app import create_app
                print("   ✅ app 模块导入成功")
            except ImportError as e:
                print(f"   ❌ app 模块导入失败: {e}")
                return False
            
            # 创建应用
            try:
                config = {
                    'DEBUG': False,
                    'TESTING': True,
                    'PLUGIN_MODE': True
                }
                app = create_app(config)
                print("   ✅ Flask应用创建成功")
            except Exception as e:
                print(f"   ❌ Flask应用创建失败: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # 4. 测试路由注册
            print("\n4. 测试路由注册...")
            
            routes = [str(rule) for rule in app.url_map.iter_rules()]
            print(f"   📋 注册了 {len(routes)} 个路由")
            
            # 检查关键路由
            key_routes = ['/health', '/testplan', '/api/testplan/cases', '/api/dashboard/statistics']
            for route in key_routes:
                if any(route in r for r in routes):
                    print(f"   ✅ 找到路由: {route}")
                else:
                    print(f"   ❌ 缺少路由: {route}")
            
            # 5. 测试应用上下文
            print("\n5. 测试应用上下文...")
            
            try:
                with app.app_context():
                    print("   ✅ Flask应用上下文正常")
                    
                    # 测试数据库连接
                    try:
                        with get_db() as conn:
                            cursor = conn.cursor()
                            cursor.execute('SELECT 1')
                            print("   ✅ 数据库连接正常")
                    except Exception as e:
                        print(f"   ⚠️ 数据库连接失败: {e}")
                        
            except Exception as e:
                print(f"   ❌ 应用上下文失败: {e}")
                return False
            
            print("\n🎉 插件启动测试完成!")
            print("✅ 所有测试通过，插件应该能正常启动")
            return True
            
        finally:
            # 恢复工作目录
            os.chdir(original_cwd)
            
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_server_startup():
    """测试服务器启动"""
    print("\n🚀 测试服务器启动...")
    
    def run_server():
        try:
            from app import create_app
            app = create_app({'TESTING': True, 'PLUGIN_MODE': True})
            app.run(host='127.0.0.1', port=5003, debug=False, use_reloader=False)
        except Exception as e:
            print(f"   ❌ 服务器启动失败: {e}")
    
    # 启动服务器线程
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    
    # 等待服务器启动
    print("   ⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 测试连接
    try:
        import requests
        response = requests.get('http://127.0.0.1:5003/health', timeout=5)
        if response.status_code == 200:
            print("   ✅ 服务器启动成功")
            data = response.json()
            print(f"   📊 健康检查: {data.get('status')}")
            return True
        else:
            print(f"   ❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 服务器连接失败: {e}")
        return False

if __name__ == '__main__':
    success1 = test_plugin_startup()
    
    if success1:
        success2 = test_server_startup()
        if success1 and success2:
            print("\n🎉 所有测试通过!")
            print("现在可以通过RunSim GUI启动仪表板了")
        else:
            print("\n⚠️ 部分测试失败")
    else:
        print("\n💥 测试失败，需要进一步调试")
