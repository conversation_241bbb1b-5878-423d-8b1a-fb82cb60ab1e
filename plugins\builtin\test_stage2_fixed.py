#!/usr/bin/env python3
"""
第二阶段修复测试脚本

验证修复后的功能
"""

import os
import sys
from datetime import datetime

def test_with_app_context():
    """测试Flask应用上下文中的功能"""
    print("测试Flask应用上下文功能...")
    
    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)
        
        try:
            from models.testplan import TestCaseManager
            from app import create_app
            
            print("  ✓ 模块导入成功")
            
            # 创建Flask应用上下文
            test_config = {
                'DATABASE_PATH': os.path.join('data', 'test_stage2_fixed.db'),
                'TESTING': True
            }
            
            app = create_app(test_config)
            print("  ✓ Flask应用创建成功")
            
            with app.app_context():
                # 测试统计功能
                stats = TestCaseManager.get_case_statistics()
                print(f"  ✓ 统计功能正常: 获取到 {len(stats)} 个统计项")
                
                # 测试用例查询
                cases, total = TestCaseManager.get_test_cases(page=1, page_size=10)
                print(f"  ✓ 用例查询功能正常: 查询到 {len(cases)} 条用例，总计 {total} 条")
                
                # 测试路由
                rules = []
                for rule in app.url_map.iter_rules():
                    if '/api/testplan' in rule.rule:
                        rules.append(rule.rule)
                
                print(f"  ✓ API路由检查: 找到 {len(rules)} 个testplan路由")
                for rule in rules[:3]:
                    print(f"    - {rule}")
            
            return True
            
        finally:
            os.chdir(original_cwd)
        
    except Exception as e:
        print(f"  ✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_functions():
    """测试Excel相关功能"""
    print("\n测试Excel功能...")
    
    try:
        # 切换到dashboard_web目录
        dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
        original_cwd = os.getcwd()
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)
        
        try:
            from utils.excel_parser import TestPlanParser
            from utils.excel_exporter import TestPlanExporter
            
            # 测试解析器
            parser = TestPlanParser()
            print(f"  ✓ Excel解析器: {len(parser.COLUMN_MAPPING)} 个列映射")
            
            # 测试导出器
            exporter = TestPlanExporter()
            print(f"  ✓ Excel导出器: {len(exporter.EXPORT_COLUMNS)} 个导出列")
            
            # 测试模板导出
            template_path = TestPlanExporter.export_template('xlsx')
            if os.path.exists(template_path):
                print(f"  ✓ 模板导出成功: {os.path.basename(template_path)}")
                os.remove(template_path)  # 清理
            else:
                print("  ✗ 模板导出失败")
                return False
            
            return True
            
        finally:
            os.chdir(original_cwd)
        
    except Exception as e:
        print(f"  ✗ Excel功能测试失败: {str(e)}")
        return False

def test_template_file():
    """测试模板文件"""
    print("\n测试模板文件...")
    
    try:
        template_path = os.path.join(os.path.dirname(__file__), 'dashboard_web', 'templates', 'testplan.html')
        
        if not os.path.exists(template_path):
            print("  ✗ 模板文件不存在")
            return False
        
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键元素
        required_elements = [
            'id="importModal"',
            'id="statusModal"', 
            'id="cases-table-body"',
            'function loadTestCases',
            'function importExcel',
        ]
        
        missing = [elem for elem in required_elements if elem not in content]
        
        if missing:
            print(f"  ✗ 模板缺少元素: {missing}")
            return False
        
        print(f"  ✓ 模板文件完整: {len(content)} 字符，包含所有必要元素")
        return True
        
    except Exception as e:
        print(f"  ✗ 模板文件测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("第二阶段修复测试")
    print("=" * 40)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查是否在正确的目录
    if not os.path.exists('plugins/builtin/dashboard_plugin.py'):
        print("❌ 错误: 请在RunSim项目根目录下运行此脚本")
        return False
    
    # 运行测试
    tests = [
        ("Flask应用上下文", test_with_app_context),
        ("Excel功能", test_excel_functions),
        ("模板文件", test_template_file),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ✗ {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 40)
    print("修复测试结果")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有修复测试通过！")
        print("\n✅ 修复的问题:")
        print("   - Flask应用上下文问题已解决")
        print("   - API路由测试问题已解决")
        print("   - 用例管理器现在可以正常工作")
        
        print("\n🚀 第二阶段功能现在完全可用:")
        print("   1. Excel文件解析和导出")
        print("   2. 用例数据管理")
        print("   3. REST API接口")
        print("   4. Web用户界面")
        
        return True
    else:
        print("\n⚠️ 部分测试仍然失败")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(0)
