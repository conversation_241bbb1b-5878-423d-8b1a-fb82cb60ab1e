#!/usr/bin/env python3
"""
阶段五：集成测试和优化脚本

该脚本执行完整的系统集成测试，包括：
1. 完整的系统集成测试
2. 性能优化和内存管理
3. 用户界面优化
4. Bug修复和功能完善
"""

import os
import sys
import time
import json
import threading
import subprocess
import requests
import psutil
from datetime import datetime
from contextlib import contextmanager

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

class Stage5IntegrationTester:
    """阶段五集成测试器"""

    def __init__(self):
        self.base_url = 'http://127.0.0.1:5000'
        self.server_process = None
        self.test_results = {
            'system_integration': {},
            'performance_optimization': {},
            'ui_optimization': {},
            'bug_fixes': {},
            'overall_score': 0
        }

    def run_complete_test(self):
        """运行完整的集成测试"""
        print("=" * 60)
        print("RunSim GUI 仪表板 - 阶段五：集成测试和优化")
        print("=" * 60)
        print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        try:
            # 1. 启动Web服务器
            print("1. 启动Web服务器...")
            if not self._start_web_server():
                print("❌ Web服务器启动失败，无法继续测试")
                return False

            # 2. 完整的系统集成测试
            print("\n2. 执行系统集成测试...")
            self._test_system_integration()

            # 3. 性能优化和内存管理测试
            print("\n3. 执行性能优化测试...")
            self._test_performance_optimization()

            # 4. 用户界面优化测试
            print("\n4. 执行用户界面优化测试...")
            self._test_ui_optimization()

            # 5. Bug修复验证
            print("\n5. 执行Bug修复验证...")
            self._test_bug_fixes()

            # 6. 生成测试报告
            print("\n6. 生成测试报告...")
            self._generate_test_report()

            return True

        except KeyboardInterrupt:
            print("\n测试被用户中断")
            return False
        except Exception as e:
            print(f"\n测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self._cleanup()

    def _start_web_server(self):
        """启动Web服务器"""
        try:
            # 检查端口是否已被占用
            if self._is_port_in_use(5000):
                print("  ✓ 端口5000已被占用，假设服务器已运行")
                return True

            # 启动Flask应用
            from app import create_app
            app = create_app()

            # 在后台线程启动服务器
            def run_server():
                app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)

            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()

            # 等待服务器启动
            for i in range(10):
                try:
                    response = requests.get(f'{self.base_url}/health', timeout=2)
                    if response.status_code == 200:
                        print(f"  ✓ Web服务器启动成功 (端口: 5000)")
                        return True
                except:
                    time.sleep(1)

            print("  ❌ Web服务器启动超时")
            return False

        except Exception as e:
            print(f"  ❌ Web服务器启动失败: {e}")
            return False

    def _is_port_in_use(self, port):
        """检查端口是否被占用"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result == 0
        except:
            return False

    def _test_system_integration(self):
        """测试系统集成"""
        print("  2.1 数据库连接测试...")
        db_test = self._test_database_connection()

        print("  2.2 API接口集成测试...")
        api_test = self._test_api_integration()

        print("  2.3 模板渲染测试...")
        template_test = self._test_template_rendering()

        print("  2.4 文件上传下载测试...")
        file_test = self._test_file_operations()

        print("  2.5 数据一致性测试...")
        consistency_test = self._test_data_consistency()

        # 记录测试结果
        self.test_results['system_integration'] = {
            'database_connection': db_test,
            'api_integration': api_test,
            'template_rendering': template_test,
            'file_operations': file_test,
            'data_consistency': consistency_test,
            'score': sum([db_test, api_test, template_test, file_test, consistency_test]) / 5 * 100
        }

        score = self.test_results['system_integration']['score']
        print(f"  系统集成测试得分: {score:.1f}/100")

    def _test_database_connection(self):
        """测试数据库连接"""
        try:
            from models.database import get_db_manager
            import os

            # 获取数据库路径
            db_path = os.path.join(os.path.dirname(__file__), 'data', 'dashboard.db')
            db_manager = get_db_manager(db_path)

            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]

            if table_count >= 4:  # 至少应该有4个表
                print("    ✓ 数据库连接正常，表结构完整")
                return True
            else:
                print(f"    ⚠️ 数据库表数量不足: {table_count}")
                return False

        except Exception as e:
            print(f"    ❌ 数据库连接失败: {e}")
            return False

    def _test_api_integration(self):
        """测试API集成"""
        api_endpoints = [
            '/api/dashboard/statistics',
            '/api/dashboard/progress',
            '/api/dashboard/case_status',
            '/api/dashboard/bug_trend',
            '/api/testplan/statistics',
            '/api/testplan/cases',
            '/api/bugs',
            '/health'
        ]

        success_count = 0
        total_count = len(api_endpoints)

        for endpoint in api_endpoints:
            try:
                response = requests.get(f'{self.base_url}{endpoint}', timeout=5)
                if response.status_code == 200:
                    success_count += 1
                    print(f"    ✓ {endpoint}")
                else:
                    print(f"    ❌ {endpoint} (状态码: {response.status_code})")
            except Exception as e:
                print(f"    ❌ {endpoint} (错误: {e})")

        success_rate = success_count / total_count
        print(f"    API集成测试: {success_count}/{total_count} ({success_rate*100:.1f}%)")
        return success_rate >= 0.8  # 80%以上成功率

    def _test_template_rendering(self):
        """测试模板渲染"""
        pages = ['/', '/testplan', '/bug']
        success_count = 0

        for page in pages:
            try:
                response = requests.get(f'{self.base_url}{page}', timeout=10)
                if response.status_code == 200 and len(response.content) > 1000:
                    success_count += 1
                    print(f"    ✓ {page} 渲染成功")
                else:
                    print(f"    ❌ {page} 渲染失败")
            except Exception as e:
                print(f"    ❌ {page} 渲染错误: {e}")

        return success_count == len(pages)

    def _test_file_operations(self):
        """测试文件操作"""
        try:
            # 测试模板下载
            response = requests.get(f'{self.base_url}/api/testplan/template', timeout=10)
            template_ok = response.status_code in [200, 503]  # 503表示功能未实现但API存在

            # 测试JSON导出
            response = requests.get(f'{self.base_url}/api/dashboard/export/json', timeout=10)
            export_ok = response.status_code == 200

            if template_ok and export_ok:
                print("    ✓ 文件操作功能正常")
                return True
            else:
                print(f"    ⚠️ 文件操作部分功能异常 (模板:{template_ok}, 导出:{export_ok})")
                return False

        except Exception as e:
            print(f"    ❌ 文件操作测试失败: {e}")
            return False

    def _test_data_consistency(self):
        """测试数据一致性"""
        try:
            # 多次请求同一API，检查数据一致性
            responses = []
            for i in range(3):
                response = requests.get(f'{self.base_url}/api/dashboard/statistics', timeout=5)
                if response.status_code == 200:
                    responses.append(response.json())
                time.sleep(0.5)

            if len(responses) >= 2:
                # 检查数据结构一致性
                keys_consistent = all(set(r.keys()) == set(responses[0].keys()) for r in responses)
                if keys_consistent:
                    print("    ✓ 数据结构一致性正常")
                    return True
                else:
                    print("    ❌ 数据结构不一致")
                    return False
            else:
                print("    ❌ 无法获取足够的响应进行一致性测试")
                return False

        except Exception as e:
            print(f"    ❌ 数据一致性测试失败: {e}")
            return False

    def _test_performance_optimization(self):
        """测试性能优化"""
        print("  3.1 响应时间测试...")
        response_test = self._test_response_times()

        print("  3.2 内存使用测试...")
        memory_test = self._test_memory_usage()

        print("  3.3 并发处理测试...")
        concurrency_test = self._test_concurrency()

        print("  3.4 数据库性能测试...")
        db_performance_test = self._test_database_performance()

        # 记录测试结果
        self.test_results['performance_optimization'] = {
            'response_times': response_test,
            'memory_usage': memory_test,
            'concurrency': concurrency_test,
            'database_performance': db_performance_test,
            'score': sum([response_test, memory_test, concurrency_test, db_performance_test]) / 4 * 100
        }

        score = self.test_results['performance_optimization']['score']
        print(f"  性能优化测试得分: {score:.1f}/100")

    def _test_response_times(self):
        """测试响应时间"""
        api_endpoints = [
            '/api/dashboard/statistics',
            '/api/dashboard/progress',
            '/api/testplan/cases',
            '/'
        ]

        total_time = 0
        success_count = 0

        for endpoint in api_endpoints:
            try:
                start_time = time.time()
                response = requests.get(f'{self.base_url}{endpoint}', timeout=10)
                end_time = time.time()

                response_time = end_time - start_time
                total_time += response_time

                if response.status_code == 200:
                    success_count += 1
                    if response_time < 1.0:
                        status = "优秀"
                    elif response_time < 2.0:
                        status = "良好"
                    else:
                        status = "需优化"
                    print(f"    {endpoint}: {response_time:.3f}s ({status})")
                else:
                    print(f"    {endpoint}: 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"    {endpoint}: 错误 ({e})")

        avg_time = total_time / len(api_endpoints) if api_endpoints else 0
        print(f"    平均响应时间: {avg_time:.3f}s")

        # 响应时间小于2秒且成功率大于80%为通过
        return avg_time < 2.0 and success_count / len(api_endpoints) >= 0.8

    def _test_memory_usage(self):
        """测试内存使用"""
        try:
            # 获取当前进程内存使用
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024

            print(f"    当前内存使用: {memory_mb:.1f}MB")

            # 执行一些操作后再次检查内存
            for i in range(10):
                requests.get(f'{self.base_url}/api/dashboard/statistics', timeout=5)

            memory_info_after = process.memory_info()
            memory_mb_after = memory_info_after.rss / 1024 / 1024
            memory_increase = memory_mb_after - memory_mb

            print(f"    操作后内存使用: {memory_mb_after:.1f}MB (增加: {memory_increase:.1f}MB)")

            # 内存使用小于100MB且增长小于10MB为通过
            return memory_mb_after < 100 and memory_increase < 10

        except Exception as e:
            print(f"    ❌ 内存测试失败: {e}")
            return False

    def _test_concurrency(self):
        """测试并发处理"""
        try:
            import concurrent.futures

            def make_request():
                try:
                    response = requests.get(f'{self.base_url}/api/dashboard/statistics', timeout=5)
                    return response.status_code == 200
                except:
                    return False

            # 并发执行10个请求
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(make_request) for _ in range(10)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]

            success_count = sum(results)
            success_rate = success_count / len(results)

            print(f"    并发测试: {success_count}/10 成功 ({success_rate*100:.1f}%)")

            return success_rate >= 0.8  # 80%成功率

        except Exception as e:
            print(f"    ❌ 并发测试失败: {e}")
            return False

    def _test_database_performance(self):
        """测试数据库性能"""
        try:
            from models.database import get_db_manager
            import os

            # 获取数据库路径
            db_path = os.path.join(os.path.dirname(__file__), 'data', 'dashboard.db')
            db_manager = get_db_manager(db_path)

            # 测试查询性能
            start_time = time.time()
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                # 执行一些查询
                cursor.execute("SELECT COUNT(*) FROM test_cases")
                cursor.execute("SELECT COUNT(*) FROM bugs")
                cursor.execute("SELECT COUNT(*) FROM projects")
            end_time = time.time()

            query_time = end_time - start_time
            print(f"    数据库查询时间: {query_time:.3f}s")

            return query_time < 0.5  # 查询时间小于0.5秒

        except Exception as e:
            print(f"    ❌ 数据库性能测试失败: {e}")
            return False

    def _test_ui_optimization(self):
        """测试用户界面优化"""
        print("  4.1 页面加载速度测试...")
        load_speed_test = self._test_page_load_speed()

        print("  4.2 静态资源测试...")
        static_resources_test = self._test_static_resources()

        print("  4.3 响应式设计测试...")
        responsive_test = self._test_responsive_design()

        print("  4.4 用户体验测试...")
        ux_test = self._test_user_experience()

        # 记录测试结果
        self.test_results['ui_optimization'] = {
            'page_load_speed': load_speed_test,
            'static_resources': static_resources_test,
            'responsive_design': responsive_test,
            'user_experience': ux_test,
            'score': sum([load_speed_test, static_resources_test, responsive_test, ux_test]) / 4 * 100
        }

        score = self.test_results['ui_optimization']['score']
        print(f"  用户界面优化测试得分: {score:.1f}/100")

    def _test_page_load_speed(self):
        """测试页面加载速度"""
        pages = ['/', '/testplan', '/bug']
        total_time = 0
        success_count = 0

        for page in pages:
            try:
                start_time = time.time()
                response = requests.get(f'{self.base_url}{page}', timeout=15)
                end_time = time.time()

                load_time = end_time - start_time
                total_time += load_time

                if response.status_code == 200:
                    success_count += 1
                    page_size = len(response.content) / 1024  # KB

                    if load_time < 2.0:
                        status = "优秀"
                    elif load_time < 5.0:
                        status = "良好"
                    else:
                        status = "需优化"

                    print(f"    {page}: {load_time:.2f}s, {page_size:.1f}KB ({status})")
                else:
                    print(f"    {page}: 加载失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"    {page}: 加载错误 ({e})")

        avg_time = total_time / len(pages) if pages else 0
        print(f"    平均加载时间: {avg_time:.2f}s")

        return avg_time < 3.0 and success_count == len(pages)

    def _test_static_resources(self):
        """测试静态资源"""
        static_resources = [
            '/static/css/bootstrap.min.css',
            '/static/js/bootstrap.min.js',
            '/static/js/jquery.min.js',
            '/static/js/chart.min.js'
        ]

        success_count = 0

        for resource in static_resources:
            try:
                response = requests.get(f'{self.base_url}{resource}', timeout=10)
                if response.status_code == 200:
                    success_count += 1
                    print(f"    ✓ {resource}")
                else:
                    print(f"    ❌ {resource} (状态码: {response.status_code})")
            except Exception as e:
                print(f"    ❌ {resource} (错误: {e})")

        # 允许部分资源缺失（可能使用CDN）
        return success_count >= len(static_resources) * 0.5

    def _test_responsive_design(self):
        """测试响应式设计"""
        try:
            # 检查页面是否包含响应式设计元素
            response = requests.get(f'{self.base_url}/', timeout=10)
            if response.status_code == 200:
                content = response.text.lower()

                # 检查Bootstrap和响应式元素
                has_bootstrap = 'bootstrap' in content
                has_viewport = 'viewport' in content
                has_responsive_classes = any(cls in content for cls in ['col-', 'container', 'row'])

                responsive_score = sum([has_bootstrap, has_viewport, has_responsive_classes])

                print(f"    Bootstrap: {'✓' if has_bootstrap else '❌'}")
                print(f"    Viewport: {'✓' if has_viewport else '❌'}")
                print(f"    响应式类: {'✓' if has_responsive_classes else '❌'}")

                return responsive_score >= 2
            else:
                print("    ❌ 无法获取页面内容")
                return False

        except Exception as e:
            print(f"    ❌ 响应式设计测试失败: {e}")
            return False

    def _test_user_experience(self):
        """测试用户体验"""
        try:
            # 检查错误处理
            response = requests.get(f'{self.base_url}/nonexistent', timeout=5)
            error_handling = response.status_code == 404

            # 检查健康检查接口
            response = requests.get(f'{self.base_url}/health', timeout=5)
            health_check = response.status_code == 200

            # 检查API错误响应
            response = requests.get(f'{self.base_url}/api/nonexistent', timeout=5)
            api_error_handling = response.status_code == 404 and 'application/json' in response.headers.get('content-type', '')

            print(f"    错误处理: {'✓' if error_handling else '❌'}")
            print(f"    健康检查: {'✓' if health_check else '❌'}")
            print(f"    API错误处理: {'✓' if api_error_handling else '❌'}")

            return sum([error_handling, health_check, api_error_handling]) >= 2

        except Exception as e:
            print(f"    ❌ 用户体验测试失败: {e}")
            return False

    def _test_bug_fixes(self):
        """测试Bug修复验证"""
        print("  5.1 已知问题修复验证...")
        known_issues_test = self._test_known_issues_fixed()

        print("  5.2 错误处理改进验证...")
        error_handling_test = self._test_error_handling_improvements()

        print("  5.3 稳定性测试...")
        stability_test = self._test_stability()

        # 记录测试结果
        self.test_results['bug_fixes'] = {
            'known_issues_fixed': known_issues_test,
            'error_handling_improved': error_handling_test,
            'stability': stability_test,
            'score': sum([known_issues_test, error_handling_test, stability_test]) / 3 * 100
        }

        score = self.test_results['bug_fixes']['score']
        print(f"  Bug修复验证得分: {score:.1f}/100")

    def _test_known_issues_fixed(self):
        """测试已知问题是否已修复"""
        try:
            # 测试testplan页面404问题是否已修复
            response = requests.get(f'{self.base_url}/testplan', timeout=10)
            testplan_fixed = response.status_code == 200

            # 测试API连接问题是否已修复
            response = requests.get(f'{self.base_url}/api/testplan/statistics', timeout=5)
            api_fixed = response.status_code == 200

            # 测试模板渲染问题是否已修复
            response = requests.get(f'{self.base_url}/bug', timeout=10)
            bug_page_fixed = response.status_code == 200

            print(f"    TestPlan页面: {'✓' if testplan_fixed else '❌'}")
            print(f"    API连接: {'✓' if api_fixed else '❌'}")
            print(f"    Bug页面: {'✓' if bug_page_fixed else '❌'}")

            return sum([testplan_fixed, api_fixed, bug_page_fixed]) >= 2

        except Exception as e:
            print(f"    ❌ 已知问题验证失败: {e}")
            return False

    def _test_error_handling_improvements(self):
        """测试错误处理改进"""
        try:
            # 测试各种错误情况的处理
            test_cases = [
                ('/api/nonexistent', 404),
                ('/nonexistent', 404),
                ('/api/testplan/case/99999', 404),  # 不存在的用例
            ]

            success_count = 0

            for url, expected_status in test_cases:
                try:
                    response = requests.get(f'{self.base_url}{url}', timeout=5)
                    if response.status_code == expected_status:
                        success_count += 1
                        print(f"    ✓ {url} -> {response.status_code}")
                    else:
                        print(f"    ❌ {url} -> {response.status_code} (期望: {expected_status})")
                except Exception as e:
                    print(f"    ❌ {url} -> 错误: {e}")

            return success_count >= len(test_cases) * 0.8

        except Exception as e:
            print(f"    ❌ 错误处理测试失败: {e}")
            return False

    def _test_stability(self):
        """测试系统稳定性"""
        try:
            # 连续请求测试
            success_count = 0
            total_requests = 20

            for i in range(total_requests):
                try:
                    response = requests.get(f'{self.base_url}/api/dashboard/statistics', timeout=3)
                    if response.status_code == 200:
                        success_count += 1
                except:
                    pass

                if i % 5 == 0:
                    print(f"    进度: {i+1}/{total_requests}")

            stability_rate = success_count / total_requests
            print(f"    稳定性测试: {success_count}/{total_requests} ({stability_rate*100:.1f}%)")

            return stability_rate >= 0.9  # 90%成功率

        except Exception as e:
            print(f"    ❌ 稳定性测试失败: {e}")
            return False

    def _generate_test_report(self):
        """生成测试报告"""
        try:
            # 计算总体得分
            scores = [
                self.test_results['system_integration']['score'],
                self.test_results['performance_optimization']['score'],
                self.test_results['ui_optimization']['score'],
                self.test_results['bug_fixes']['score']
            ]

            overall_score = sum(scores) / len(scores)
            self.test_results['overall_score'] = overall_score

            # 生成报告
            report = {
                'test_time': datetime.now().isoformat(),
                'overall_score': overall_score,
                'test_results': self.test_results,
                'summary': self._generate_summary()
            }

            # 保存报告到文件
            report_file = f'stage5_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            print(f"  测试报告已保存: {report_file}")
            print(f"  总体得分: {overall_score:.1f}/100")

            # 显示摘要
            self._display_summary()

        except Exception as e:
            print(f"  ❌ 生成测试报告失败: {e}")

    def _generate_summary(self):
        """生成测试摘要"""
        summary = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'recommendations': []
        }

        # 统计测试结果
        for category, results in self.test_results.items():
            if category != 'overall_score' and isinstance(results, dict):
                for test_name, result in results.items():
                    if test_name != 'score' and isinstance(result, bool):
                        summary['total_tests'] += 1
                        if result:
                            summary['passed_tests'] += 1
                        else:
                            summary['failed_tests'] += 1

        # 生成建议
        if self.test_results['performance_optimization']['score'] < 80:
            summary['recommendations'].append("建议优化系统性能，特别是响应时间和内存使用")

        if self.test_results['ui_optimization']['score'] < 80:
            summary['recommendations'].append("建议改进用户界面，优化页面加载速度")

        if self.test_results['bug_fixes']['score'] < 90:
            summary['recommendations'].append("建议继续修复已知问题，提高系统稳定性")

        return summary

    def _display_summary(self):
        """显示测试摘要"""
        print("\n" + "=" * 50)
        print("测试摘要")
        print("=" * 50)

        summary = self._generate_summary()

        print(f"总测试数: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"通过率: {summary['passed_tests']/summary['total_tests']*100:.1f}%")

        print("\n各模块得分:")
        print(f"  系统集成: {self.test_results['system_integration']['score']:.1f}/100")
        print(f"  性能优化: {self.test_results['performance_optimization']['score']:.1f}/100")
        print(f"  界面优化: {self.test_results['ui_optimization']['score']:.1f}/100")
        print(f"  Bug修复: {self.test_results['bug_fixes']['score']:.1f}/100")

        if summary['recommendations']:
            print("\n改进建议:")
            for i, rec in enumerate(summary['recommendations'], 1):
                print(f"  {i}. {rec}")

        # 评级
        overall_score = self.test_results['overall_score']
        if overall_score >= 90:
            grade = "优秀 🎉"
        elif overall_score >= 80:
            grade = "良好 👍"
        elif overall_score >= 70:
            grade = "及格 ✅"
        else:
            grade = "需改进 ⚠️"

        print(f"\n总体评级: {grade} ({overall_score:.1f}/100)")

    def _cleanup(self):
        """清理资源"""
        try:
            if self.server_process:
                self.server_process.terminate()
                print("  Web服务器已停止")
        except:
            pass

def main():
    """主函数"""
    tester = Stage5IntegrationTester()
    success = tester.run_complete_test()

    if success:
        print("\n🎉 阶段五集成测试和优化完成！")
        return 0
    else:
        print("\n❌ 阶段五测试失败，请检查错误信息")
        return 1

if __name__ == '__main__':
    sys.exit(main())
