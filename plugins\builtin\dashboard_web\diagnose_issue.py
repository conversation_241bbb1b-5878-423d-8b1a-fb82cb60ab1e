#!/usr/bin/env python3
"""
诊断NOT FOUND问题

检查可能导致404错误的各种原因
"""

import os
import sys
import socket
import requests
from datetime import datetime

def check_port_availability():
    """检查端口5000是否可用"""
    print("检查端口5000可用性...")
    
    try:
        # 检查端口是否被占用
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', 5000))
        sock.close()
        
        if result == 0:
            print("  ⚠️  端口5000已被占用")
            
            # 尝试访问现有服务
            try:
                response = requests.get('http://127.0.0.1:5000', timeout=5)
                print(f"  📡 现有服务响应: {response.status_code}")
                
                # 测试testplan路由
                response = requests.get('http://127.0.0.1:5000/testplan', timeout=5)
                print(f"  📡 /testplan 响应: {response.status_code}")
                
                if response.status_code == 404:
                    print("  ❌ /testplan 路由返回404 - 这就是问题所在！")
                    return False
                elif response.status_code == 200:
                    print("  ✅ /testplan 路由正常工作")
                    return True
                else:
                    print(f"  ⚠️  /testplan 路由返回异常状态码: {response.status_code}")
                    return False
                    
            except requests.exceptions.RequestException as e:
                print(f"  ❌ 无法连接到现有服务: {e}")
                return False
        else:
            print("  ✅ 端口5000可用")
            return True
            
    except Exception as e:
        print(f"  ❌ 检查端口时出错: {e}")
        return False

def test_flask_app_directly():
    """直接测试Flask应用"""
    print("\n直接测试Flask应用...")
    
    try:
        # 切换到正确的目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # 添加当前目录到Python路径
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        # 导入Flask应用
        from app import create_app
        
        # 创建应用
        app = create_app()
        print("  ✅ Flask应用创建成功")
        
        # 使用测试客户端
        with app.test_client() as client:
            # 测试主页
            response = client.get('/')
            print(f"  📡 GET / : {response.status_code}")
            
            # 测试testplan页面
            response = client.get('/testplan')
            print(f"  📡 GET /testplan : {response.status_code}")
            
            if response.status_code == 200:
                print("  ✅ /testplan 路由在测试环境中正常工作")
                return True
            else:
                print(f"  ❌ /testplan 路由在测试环境中返回: {response.status_code}")
                print(f"  📄 响应内容: {response.data.decode('utf-8')[:200]}")
                return False
        
    except Exception as e:
        print(f"  ❌ 测试Flask应用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_template_files():
    """检查模板文件"""
    print("\n检查模板文件...")
    
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        templates_dir = os.path.join(script_dir, 'templates')
        
        required_templates = [
            'base.html',
            'dashboard.html', 
            'testplan.html',
            'bug.html',
            'error.html'
        ]
        
        all_exist = True
        for template in required_templates:
            template_path = os.path.join(templates_dir, template)
            if os.path.exists(template_path):
                # 检查文件大小
                size = os.path.getsize(template_path)
                print(f"  ✅ {template} ({size} 字节)")
            else:
                print(f"  ❌ {template} 不存在")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"  ❌ 检查模板文件失败: {e}")
        return False

def check_database():
    """检查数据库"""
    print("\n检查数据库...")
    
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            from models.database import get_db
            
            # 尝试连接数据库
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                print(f"  ✅ 数据库连接成功，包含 {len(tables)} 个表")
                for table in tables:
                    print(f"    - {table['name']}")
                
                return True
        
    except Exception as e:
        print(f"  ❌ 数据库检查失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 可能的解决方案:")
    print("=" * 40)
    
    print("1. 重启RunSim GUI:")
    print("   - 完全关闭RunSim GUI")
    print("   - 重新启动程序")
    print("   - 重新加载仪表板插件")
    
    print("\n2. 清除浏览器缓存:")
    print("   - 按 Ctrl+Shift+Delete")
    print("   - 清除缓存和Cookie")
    print("   - 刷新页面")
    
    print("\n3. 检查端口冲突:")
    print("   - 关闭其他可能占用5000端口的程序")
    print("   - 重启RunSim GUI")
    
    print("\n4. 手动测试:")
    print("   - 直接访问 http://127.0.0.1:5000")
    print("   - 然后访问 http://127.0.0.1:5000/testplan")
    
    print("\n5. 查看RunSim GUI控制台:")
    print("   - 检查是否有错误信息")
    print("   - 确认Web服务器启动成功")

def main():
    """主诊断函数"""
    print("NOT FOUND 问题诊断工具")
    print("=" * 40)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查是否在正确的目录
    if not os.path.exists('app.py'):
        print("❌ 错误: 请在dashboard_web目录下运行此脚本")
        return False
    
    # 执行诊断步骤
    checks = [
        ("模板文件检查", check_template_files),
        ("数据库检查", check_database),
        ("Flask应用测试", test_flask_app_directly),
        ("端口可用性检查", check_port_availability),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"✗ {check_name} 执行异常: {str(e)}")
            results.append((check_name, False))
    
    # 输出诊断结果
    print("\n" + "=" * 40)
    print("诊断结果汇总")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 所有检查都通过！")
        print("如果仍然出现NOT FOUND错误，问题可能在于:")
        print("- RunSim GUI中的Web服务器没有正确启动")
        print("- 浏览器缓存问题")
        print("- 网络连接问题")
    else:
        print("\n⚠️ 发现问题，请查看上面的详细信息")
    
    # 提供解决方案
    provide_solutions()
    
    return passed == total

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n诊断被用户中断")
        sys.exit(0)
