"""
Excel文件解析器

该模块负责解析TestPlan Excel文件，支持多种格式的用例表格。
"""

import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import openpyxl
from openpyxl.worksheet.worksheet import Worksheet

# 配置日志
logger = logging.getLogger(__name__)

class TestPlanParser:
    """TestPlan Excel文件解析器"""

    # 标准列映射（中英文对照）
    COLUMN_MAPPING = {
        # 基本信息
        '类别': 'category',
        'category': 'category',
        'test category': 'category',
        '编号': 'number',
        'number': 'number',
        'items': 'number',

        # 修改后的表头字段
        '测试区域': 'test_areas',
        'test areas': 'test_areas',
        '测试范围': 'test_scope',
        'test scope': 'test_scope',
        '功能点': 'function_point',
        'function points': 'function_point',
        'function_point': 'function_point',
        '检查点': 'check_point',
        'check point': 'check_point',
        'check_point': 'check_point',
        '覆盖': 'cover',
        'cover': 'cover',
        '测试用例名称': 'case_name',
        'testcase name': 'case_name',
        'case_name': 'case_name',
        'testcase': 'case_name',

        # 兼容旧格式
        '测试流程': 'test_process',
        'test_process': 'test_process',
        '覆盖点': 'coverage_point',
        'coverage_point': 'coverage_point',
        '用例名称': 'case_name',

        # 时间信息
        '开始时间': 'start_time',
        'start_time': 'start_time',
        '结束时间': 'end_time',
        'end_time': 'end_time',
        '实际时间': 'actual_time',
        'actual_time': 'actual_time',

        # 验证阶段字段 (新格式)
        'subsys': 'subsys_phase',  # M列 - Subsys Phase
        'top': 'top_phase',        # O列 - TOP Phase
        'post_subsys': 'post_subsys_phase',  # Q列 - POST_Subsys Phase
        'post_top': 'post_top_phase',        # S列 - POST_TOP Phase

        # 状态字段
        'subsys_status': 'subsys_status',     # N列 - Subsys Status
        'top_status': 'top_status',           # P列 - TOP Status
        'post_subsys_status': 'post_subsys_status',  # R列 - POST_Subsys Status
        'post_top_status': 'post_top_status',        # T列 - POST_TOP Status

        # 兼容旧格式的阶段和状态
        '子系统级阶段': 'subsys_stage',
        'subsys_stage': 'subsys_stage',
        '子系统级状态': 'subsys_status',
        'top级阶段': 'top_stage',
        'top_stage': 'top_stage',
        'top级状态': 'top_status',
        '后仿子系统阶段': 'post_subsys_stage',
        'post_subsys_stage': 'post_subsys_stage',
        '后仿子系统状态': 'post_subsys_status',
        '后仿top阶段': 'post_top_stage',
        'post_top_stage': 'post_top_stage',
        '后仿top状态': 'post_top_status',
        'post_top_status': 'post_top_status',

        # 备注
        '备注': 'remarks',
        'remarks': 'remarks',
        'comment': 'remarks',
        'note': 'remarks',

        # 新增字段支持
        'owner': 'owner',
        '负责人': 'owner',
        '所有者': 'owner',

        # 阶段字段的英文支持
        'phase': 'subsys_stage',  # 默认映射到subsys_stage
        'status': 'subsys_status', # 默认映射到subsys_status
    }

    # 状态标准化映射（符合TestPlan规范）
    STATUS_MAPPING = {
        # PASS状态 - 表示用例已通过测试
        'pass': 'PASS',
        'passed': 'PASS',
        'success': 'PASS',
        'ok': 'PASS',
        '通过': 'PASS',
        'PASS': 'PASS',

        # Pending状态 - 表示用例未开始调试
        'pending': 'Pending',
        'not started': 'Pending',
        'not_started': 'Pending',
        'todo': 'Pending',
        'waiting': 'Pending',
        '未开始': 'Pending',
        '待开始': 'Pending',
        'Pending': 'Pending',

        # On-Going状态 - 表示用例正在调试中
        'on-going': 'On-Going',
        'ongoing': 'On-Going',
        'running': 'On-Going',
        'in progress': 'On-Going',
        'debugging': 'On-Going',
        '进行中': 'On-Going',
        '调试中': 'On-Going',
        'On-Going': 'On-Going',

        # N/A状态 - 根据Phase字段值有不同含义
        'n/a': 'N/A',
        'na': 'N/A',
        'none': 'N/A',
        'null': 'N/A',
        '': 'N/A',
        None: 'N/A',
        'N/A': 'N/A',
        'NA': 'N/A',

        # 兼容旧格式（映射到新状态）
        'fail': 'Pending',  # 旧的Fail状态映射为Pending（需要重新调试）
        'failed': 'Pending',
        'failure': 'Pending',
        'error': 'Pending',
        '失败': 'Pending',
        'Fail': 'Pending',
        'FAIL': 'Pending',
        'Not Started': 'Pending',  # 旧的Not Started映射为Pending
        'Pass': 'PASS',  # 兼容旧的Pass格式
    }

    def __init__(self):
        """初始化解析器"""
        self.workbook = None
        self.worksheet = None
        self.header_row = None
        self.column_mapping = {}

    def parse_file(self, file_path: str, sheet_name: Optional[str] = None) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        解析Excel文件

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称，如果为None则使用第一个工作表

        Returns:
            Tuple[List[Dict], Dict]: (用例数据列表, 解析信息)
        """
        try:
            logger.info(f"开始解析Excel文件: {file_path}")

            # 打开工作簿
            self.workbook = openpyxl.load_workbook(file_path, data_only=True)

            # 选择工作表
            if sheet_name:
                if sheet_name in self.workbook.sheetnames:
                    self.worksheet = self.workbook[sheet_name]
                else:
                    raise ValueError(f"工作表 '{sheet_name}' 不存在")
            else:
                self.worksheet = self.workbook.active

            logger.info(f"使用工作表: {self.worksheet.title}")

            # 查找表头
            self._find_header_row()

            # 建立列映射
            self._build_column_mapping()

            # 解析数据
            test_cases = self._parse_data_rows()

            # 生成解析信息
            parse_info = {
                'file_name': os.path.basename(file_path),
                'sheet_name': self.worksheet.title,
                'total_rows': self.worksheet.max_row,
                'header_row': self.header_row,
                'data_rows': len(test_cases),
                'columns_found': list(self.column_mapping.keys()),
                'parse_time': datetime.now().isoformat()
            }

            logger.info(f"解析完成，共解析 {len(test_cases)} 条用例")
            return test_cases, parse_info

        except Exception as e:
            logger.error(f"解析Excel文件失败: {str(e)}")
            raise
        finally:
            if self.workbook:
                self.workbook.close()

    def _find_header_row(self) -> None:
        """查找表头行"""
        # 特殊处理：优先检查TestPlan标准格式（第3-4行合并表头）
        if self.worksheet.max_row >= 4:
            # 检查第3行是否包含TestPlan标准表头
            row3_values = [str(cell.value).lower().strip() if cell.value else ''
                          for cell in self.worksheet[3]]
            testplan_headers = ['test category', 'items', 'test areas', 'testcase name']
            if any(header in ' '.join(row3_values) for header in testplan_headers):
                self.header_row = 3
                logger.info(f"找到TestPlan标准格式表头行: 第3行")
                return

        # 在前10行中查找包含关键列名的行
        for row_num in range(1, min(11, self.worksheet.max_row + 1)):
            row_values = [str(cell.value).lower().strip() if cell.value else ''
                         for cell in self.worksheet[row_num]]

            # 检查是否包含关键列名（支持新旧格式）
            key_columns = [
                '用例名称', 'case_name', 'testcase', 'testcase name',
                '类别', 'category', 'test category',
                'items', 'test areas', 'function points'
            ]
            if any(key_col.lower() in ' '.join(row_values) for key_col in key_columns):
                self.header_row = row_num
                logger.info(f"找到表头行: 第{row_num}行")
                return

        # 如果没找到，默认使用第一行
        self.header_row = 1
        logger.warning("未找到明确的表头行，使用第1行作为表头")

    def _build_column_mapping(self) -> None:
        """建立列映射关系"""
        self.column_mapping = {}

        if not self.header_row:
            return

        # 检查是否为TestPlan标准格式（第3-4行合并表头）
        if self.header_row == 3 and self.worksheet.max_row >= 4:
            self._build_testplan_column_mapping()
        else:
            self._build_standard_column_mapping()

        logger.info(f"建立列映射完成，共映射 {len(self.column_mapping)} 列")

    def _build_testplan_column_mapping(self) -> None:
        """建立TestPlan标准格式的列映射关系"""
        # TestPlan标准格式的固定列映射
        testplan_mapping = {
            1: 'category',           # A: Test Category
            2: 'number',             # B: Items
            3: 'test_areas',         # C: Test Areas
            4: 'function_point',     # D: Function points
            5: 'test_scope',         # E: Test Scope
            6: 'check_point',        # F: Check Point
            7: 'cover',              # G: Cover
            8: 'case_name',          # H: TestCase Name
            9: 'start_time',         # I: Start Time
            10: 'end_time',          # J: End Time
            11: 'actual_time',       # K: Actual Time
            12: 'owner',             # L: Owner
            13: 'subsys_phase',      # M: Subsys Phase
            14: 'subsys_status',     # N: Subsys Status
            15: 'top_phase',         # O: TOP Phase
            16: 'top_status',        # P: TOP Status
            17: 'post_subsys_phase', # Q: POST_Subsys Phase
            18: 'post_subsys_status',# R: POST_Subsys Status
            19: 'post_top_phase',    # S: POST_TOP Phase
            20: 'post_top_status',   # T: POST_TOP Status
            21: 'remarks'            # U: Note
        }

        # 验证表头内容并建立映射
        row3_cells = self.worksheet[3]
        row4_cells = self.worksheet[4] if self.worksheet.max_row >= 4 else []

        for col_idx, field_name in testplan_mapping.items():
            if col_idx <= len(row3_cells):
                # 验证表头内容是否匹配预期
                row3_value = str(row3_cells[col_idx - 1].value).strip() if row3_cells[col_idx - 1].value else ''
                row4_value = str(row4_cells[col_idx - 1].value).strip() if col_idx <= len(row4_cells) and row4_cells[col_idx - 1].value else ''

                self.column_mapping[col_idx] = field_name
                logger.debug(f"TestPlan列映射: 第{col_idx}列 '{row3_value}/{row4_value}' -> '{field_name}'")

    def _build_standard_column_mapping(self) -> None:
        """建立标准格式的列映射关系"""
        # 获取表头行的所有值
        header_cells = self.worksheet[self.header_row]

        for col_idx, cell in enumerate(header_cells, 1):
            if cell.value:
                header_name = str(cell.value).strip()

                # 查找对应的标准字段名
                for key, standard_name in self.COLUMN_MAPPING.items():
                    if key.lower() == header_name.lower():
                        self.column_mapping[col_idx] = standard_name
                        logger.debug(f"列映射: 第{col_idx}列 '{header_name}' -> '{standard_name}'")
                        break

    def _parse_data_rows(self) -> List[Dict[str, Any]]:
        """解析数据行"""
        test_cases = []

        # 确定数据起始行
        if self.header_row == 3:
            # TestPlan标准格式，数据从第5行开始
            start_row = 5
        else:
            # 其他格式，从表头行的下一行开始解析
            start_row = self.header_row + 1 if self.header_row else 2

        for row_num in range(start_row, self.worksheet.max_row + 1):
            row_data = self._parse_single_row(row_num)

            # 检查是否为有效的用例行（至少要有用例名称）
            if row_data and row_data.get('case_name'):
                test_cases.append(row_data)

        return test_cases

    def _parse_single_row(self, row_num: int) -> Optional[Dict[str, Any]]:
        """解析单行数据"""
        try:
            row_data = {}
            row_cells = self.worksheet[row_num]

            # 解析每一列
            for col_idx, cell in enumerate(row_cells, 1):
                if col_idx in self.column_mapping:
                    field_name = self.column_mapping[col_idx]
                    cell_value = self._parse_cell_value(cell, field_name)

                    if cell_value is not None:
                        row_data[field_name] = cell_value

            # 如果没有用例名称，跳过这行
            if not row_data.get('case_name'):
                return None

            # 设置默认值
            self._set_default_values(row_data)

            return row_data

        except Exception as e:
            logger.warning(f"解析第{row_num}行时出错: {str(e)}")
            return None

    def _parse_cell_value(self, cell, field_name: str) -> Any:
        """解析单元格值"""
        if cell.value is None:
            return None

        value = cell.value

        # 处理不同类型的字段
        if field_name in ['start_time', 'end_time']:
            # 时间字段
            if isinstance(value, datetime):
                return value
            elif isinstance(value, str):
                # 尝试解析字符串时间
                try:
                    return datetime.strptime(value.strip(), '%Y-%m-%d %H:%M:%S')
                except:
                    try:
                        return datetime.strptime(value.strip(), '%Y-%m-%d')
                    except:
                        return None

        elif field_name == 'actual_time':
            # 实际时间（分钟）
            if isinstance(value, (int, float)):
                return int(value)
            elif isinstance(value, str):
                try:
                    return int(float(value.strip()))
                except:
                    return None

        elif field_name in ['subsys_status', 'top_status', 'post_subsys_status', 'post_top_status']:
            # 状态字段，需要标准化
            status_str = str(value).strip().lower()
            return self.STATUS_MAPPING.get(status_str, str(value).strip())

        elif field_name in ['subsys_phase', 'top_phase']:
            # 验证阶段字段 (DVR1/DVR2/DVR3/DVS1/DVS2 或 N/A)
            phase_str = str(value).strip().upper()
            valid_phases = ['DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2', 'N/A']
            if phase_str in valid_phases:
                return phase_str
            elif phase_str in ['', 'NONE', 'NULL']:
                return 'N/A'
            else:
                return phase_str  # 保持原值，由验证函数处理

        elif field_name in ['post_subsys_phase', 'post_top_phase']:
            # 后仿阶段字段 (√ 或空白)
            phase_str = str(value).strip()
            if phase_str in ['√', '✓', 'YES', 'Y', '1', 'TRUE']:
                return '√'
            else:
                return ''  # 空白表示不跑后仿

        else:
            # 其他字段，转换为字符串
            return str(value).strip() if value else None

    def _set_default_values(self, row_data: Dict[str, Any]) -> None:
        """设置默认值"""
        # 状态字段默认值
        status_fields = ['subsys_status', 'top_status', 'post_subsys_status', 'post_top_status']
        for field in status_fields:
            if field not in row_data or not row_data[field]:
                row_data[field] = 'Pending'  # 新的默认状态为Pending

        # 验证阶段字段默认值
        phase_fields = ['subsys_phase', 'top_phase']
        for field in phase_fields:
            if field not in row_data or not row_data[field]:
                row_data[field] = 'N/A'

        # 后仿阶段字段默认值
        post_phase_fields = ['post_subsys_phase', 'post_top_phase']
        for field in post_phase_fields:
            if field not in row_data:
                row_data[field] = ''

        # 其他字段默认值
        if 'category' not in row_data:
            row_data['category'] = ''
        if 'number' not in row_data:
            row_data['number'] = ''
        if 'owner' not in row_data:
            row_data['owner'] = ''

    @staticmethod
    def validate_test_cases(test_cases: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        验证用例数据

        Args:
            test_cases: 用例数据列表

        Returns:
            Tuple[List[Dict], List[str]]: (有效用例列表, 错误信息列表)
        """
        valid_cases = []
        errors = []

        for i, case in enumerate(test_cases, 1):
            case_errors = []

            # 必填字段检查
            if not case.get('case_name'):
                case_errors.append("缺少用例名称")

            # 用例名称唯一性检查
            case_name = case.get('case_name')
            if case_name:
                existing_names = [c.get('case_name') for c in valid_cases]
                if case_name in existing_names:
                    case_errors.append(f"用例名称重复: {case_name}")

            # 状态值检查（符合TestPlan规范）
            valid_statuses = ['PASS', 'Pending', 'On-Going', 'N/A']
            status_fields = ['subsys_status', 'top_status', 'post_subsys_status', 'post_top_status']
            for field in status_fields:
                status = case.get(field)
                if status and status not in valid_statuses:
                    case_errors.append(f"无效的状态值 {field}: {status}")

            # 验证阶段值检查
            valid_phases = ['DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2', 'N/A']
            phase_fields = ['subsys_phase', 'top_phase']
            for field in phase_fields:
                phase = case.get(field)
                if phase and phase not in valid_phases:
                    case_errors.append(f"无效的验证阶段值 {field}: {phase}")

            # 后仿阶段值检查
            post_phase_fields = ['post_subsys_phase', 'post_top_phase']
            for field in post_phase_fields:
                post_phase = case.get(field)
                if post_phase and post_phase not in ['√', '']:
                    case_errors.append(f"无效的后仿阶段值 {field}: {post_phase}")

            if case_errors:
                errors.append(f"第{i}行: {'; '.join(case_errors)}")
            else:
                valid_cases.append(case)

        return valid_cases, errors
