#!/usr/bin/env python3
import openpyxl

try:
    wb = openpyxl.load_workbook("TestPlan_Template.xlsx")
    ws = wb["TP"]
    
    print("✅ TestPlan表格修改完成!")
    print("=" * 40)
    
    # 检查修改后的表头
    print("📋 修改后的表头:")
    print(f"  C列: {ws['C3'].value}")  # Test Areas
    print(f"  E列: {ws['E3'].value}")  # Test Scope  
    print(f"  G列: {ws['G3'].value}")  # Cover
    print(f"  H列: {ws['H3'].value}")  # TestCase Name
    
    print(f"\n📊 表格信息:")
    print(f"  行数: {ws.max_row}")
    print(f"  列数: {ws.max_column}")
    
    # 检查示例数据
    print(f"\n📝 示例数据:")
    for row in range(5, 8):
        test_areas = ws.cell(row=row, column=3).value  # C列
        test_scope = ws.cell(row=row, column=5).value  # E列
        cover = ws.cell(row=row, column=7).value       # G列
        testcase_name = ws.cell(row=row, column=8).value # H列
        if test_areas or testcase_name:
            print(f"  第{row}行:")
            print(f"    Test Areas: {test_areas}")
            print(f"    Test Scope: {test_scope}")
            print(f"    Cover: {cover}")
            print(f"    TestCase Name: {testcase_name}")
    
    wb.close()
    print("\n🎉 表头修改验证成功!")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
