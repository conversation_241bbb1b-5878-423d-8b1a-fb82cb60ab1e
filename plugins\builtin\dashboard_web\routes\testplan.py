"""
用例管理API路由

该模块定义了用例管理相关的REST API接口。
"""

import os
import sys
import logging
import tempfile
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, send_file
from werkzeug.utils import secure_filename

# 确保能找到模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 使用importlib动态导入，避免模块冲突
def _get_modules():
    """动态导入所需模块"""
    import importlib.util

    modules = {}

    # 导入TestPlanParser
    try:
        excel_parser_file = os.path.join(parent_dir, 'utils', 'excel_parser.py')
        if os.path.exists(excel_parser_file):
            spec = importlib.util.spec_from_file_location("routes_excel_parser", excel_parser_file)
            excel_parser_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(excel_parser_module)
            modules['TestPlanParser'] = getattr(excel_parser_module, 'TestPlanParser')
    except Exception as e:
        logger.warning(f"导入TestPlanParser失败: {e}")
        modules['TestPlanParser'] = None

    # 导入TestCaseManager
    try:
        testplan_file = os.path.join(parent_dir, 'models', 'testplan.py')
        if os.path.exists(testplan_file):
            spec = importlib.util.spec_from_file_location("routes_testplan", testplan_file)
            testplan_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(testplan_module)
            modules['TestCaseManager'] = getattr(testplan_module, 'TestCaseManager')
    except Exception as e:
        logger.warning(f"导入TestCaseManager失败: {e}")
        modules['TestCaseManager'] = None

    # 导入TestPlanExporter
    try:
        excel_exporter_file = os.path.join(parent_dir, 'utils', 'excel_exporter.py')
        if os.path.exists(excel_exporter_file):
            spec = importlib.util.spec_from_file_location("routes_excel_exporter", excel_exporter_file)
            excel_exporter_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(excel_exporter_module)
            modules['TestPlanExporter'] = getattr(excel_exporter_module, 'TestPlanExporter')
    except Exception as e:
        logger.warning(f"导入TestPlanExporter失败: {e}")
        modules['TestPlanExporter'] = None

    return modules

# 获取模块
_modules = _get_modules()
TestPlanParser = _modules.get('TestPlanParser')
TestCaseManager = _modules.get('TestCaseManager')
TestPlanExporter = _modules.get('TestPlanExporter')

# 配置日志
logger = logging.getLogger(__name__)

# 创建蓝图
testplan_bp = Blueprint('testplan', __name__)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@testplan_bp.route('/cases', methods=['GET'])
def get_test_cases():
    """
    获取用例列表

    Query Parameters:
        page: 页码 (默认1)
        page_size: 每页数量 (默认50)
        search: 搜索关键词
        status: 状态过滤
        project_id: 项目ID
    """
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        search = request.args.get('search', '').strip()
        status_filter = request.args.get('status', '').strip()
        project_id = request.args.get('project_id')

        if project_id:
            project_id = int(project_id)

        # 参数验证
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 200:
            page_size = 50

        # 获取用例数据
        cases, total_count = TestCaseManager.get_test_cases(
            project_id=project_id,
            page=page,
            page_size=page_size,
            search=search if search else None,
            status_filter=status_filter if status_filter else None
        )

        # 计算分页信息
        total_pages = (total_count + page_size - 1) // page_size

        return jsonify({
            'success': True,
            'data': {
                'cases': cases,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取用例列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取用例列表失败',
            'message': str(e)
        }), 500

@testplan_bp.route('/import', methods=['POST'])
def import_excel():
    """
    导入Excel文件

    Form Data:
        file: Excel文件
        project_id: 项目ID (可选)
        sheet_name: 工作表名称 (可选)
    """
    try:
        # 检查文件
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有上传文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '没有选择文件'
            }), 400

        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': '不支持的文件格式，请上传Excel文件(.xlsx, .xls)'
            }), 400

        # 获取参数
        project_id = request.form.get('project_id')
        if project_id:
            project_id = int(project_id)

        sheet_name = request.form.get('sheet_name')

        # 保存临时文件
        filename = secure_filename(file.filename)
        temp_dir = tempfile.gettempdir()
        temp_path = os.path.join(temp_dir, f"upload_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{filename}")

        try:
            file.save(temp_path)

            # 解析Excel文件
            parser = TestPlanParser()
            test_cases, parse_info = parser.parse_file(temp_path, sheet_name)

            # 验证数据
            valid_cases, validation_errors = TestPlanParser.validate_test_cases(test_cases)

            if not valid_cases:
                return jsonify({
                    'success': False,
                    'error': '没有有效的用例数据',
                    'validation_errors': validation_errors,
                    'parse_info': parse_info
                }), 400

            # 批量导入
            import_result = TestCaseManager.batch_import_cases(valid_cases, project_id)

            # 返回结果
            result = {
                'success': True,
                'data': {
                    'total_parsed': len(test_cases),
                    'valid_cases': len(valid_cases),
                    'processed_count': import_result['success_count'],
                    'inserted_count': import_result['inserted_count'],
                    'updated_count': import_result['updated_count'],
                    'failed_count': len(import_result['errors']),
                    'parse_info': parse_info
                },
                'timestamp': datetime.now().isoformat()
            }

            if validation_errors:
                result['validation_errors'] = validation_errors

            if import_result['errors']:
                result['import_errors'] = import_result['errors']

            return jsonify(result)

        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)

    except Exception as e:
        logger.error(f"导入Excel文件失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '导入失败',
            'message': str(e)
        }), 500

@testplan_bp.route('/export', methods=['GET'])
def export_excel():
    """
    导出Excel文件

    Query Parameters:
        project_id: 项目ID (可选)
        format: 导出格式 (默认xlsx)
    """
    try:
        project_id = request.args.get('project_id')
        if project_id:
            project_id = int(project_id)

        export_format = request.args.get('format', 'xlsx')

        # 获取用例数据
        cases, total_count = TestCaseManager.get_test_cases(
            project_id=project_id,
            page=1,
            page_size=10000  # 导出所有数据
        )

        if not cases:
            return jsonify({
                'success': False,
                'error': '没有可导出的数据'
            }), 400

        # 创建导出器并生成文件
        exporter = TestPlanExporter()
        temp_path = exporter.export_to_excel(cases, export_format)

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"testplan_export_{timestamp}.{export_format}"

        return send_file(
            temp_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        logger.error(f"导出Excel文件失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '导出失败',
            'message': str(e)
        }), 500

@testplan_bp.route('/case/<int:case_id>', methods=['PUT'])
def update_case_status(case_id):
    """
    更新用例状态

    Path Parameters:
        case_id: 用例ID

    JSON Body:
        stage_type: 阶段类型 ('subsys', 'top', 'post_subsys', 'post_top')
        status: 新状态 ('Pass', 'Fail', 'On-Going', 'Not Started')
        changed_by: 修改人 (可选)
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': '缺少请求数据'
            }), 400

        stage_type = data.get('stage_type')
        status = data.get('status')
        changed_by = data.get('changed_by', 'web_user')

        # 参数验证
        valid_stages = ['subsys', 'top', 'post_subsys', 'post_top']
        valid_statuses = ['PASS', 'Pending', 'On-Going', 'N/A']

        if stage_type not in valid_stages:
            return jsonify({
                'success': False,
                'error': f'无效的阶段类型: {stage_type}'
            }), 400

        if status not in valid_statuses:
            return jsonify({
                'success': False,
                'error': f'无效的状态: {status}'
            }), 400

        # 更新状态
        success = TestCaseManager.update_case_status(case_id, stage_type, status, changed_by)

        if success:
            return jsonify({
                'success': True,
                'message': '状态更新成功',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': '状态更新失败'
            }), 500

    except Exception as e:
        logger.error(f"更新用例状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '更新失败',
            'message': str(e)
        }), 500

@testplan_bp.route('/template', methods=['GET'])
def download_template():
    """
    下载用例模板文件

    Query Parameters:
        format: 文件格式 (默认xlsx)
    """
    try:
        file_format = request.args.get('format', 'xlsx')

        # 生成模板文件
        if TestPlanExporter:
            temp_path = TestPlanExporter.export_template(file_format)
        else:
            raise ImportError("TestPlanExporter模块不可用")

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"testplan_template_{timestamp}.{file_format}"

        return send_file(
            temp_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        logger.error(f"下载模板失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '下载模板失败',
            'message': str(e)
        }), 500

@testplan_bp.route('/case/<int:case_id>', methods=['GET'])
def get_case(case_id):
    """
    获取单个用例信息

    Path Parameters:
        case_id: 用例ID
    """
    try:
        # 动态导入database模块
        import importlib.util
        database_file = os.path.join(parent_dir, 'models', 'database.py')
        spec = importlib.util.spec_from_file_location("routes_database_1", database_file)
        database_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(database_module)
        get_db = getattr(database_module, 'get_db')

        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM test_cases WHERE id = ?", (case_id,))
            result = cursor.fetchone()

            if result:
                case_data = dict(result)
                return jsonify({
                    'success': True,
                    'data': case_data,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '用例不存在'
                }), 404

    except Exception as e:
        logger.error(f"获取用例信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取用例信息失败',
            'message': str(e)
        }), 500

@testplan_bp.route('/case/<int:case_id>', methods=['DELETE'])
def delete_case(case_id):
    """
    删除用例

    Path Parameters:
        case_id: 用例ID
    """
    try:
        success = TestCaseManager.delete_test_case(case_id)

        if success:
            return jsonify({
                'success': True,
                'message': '用例删除成功',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': '用例不存在或删除失败'
            }), 404

    except Exception as e:
        logger.error(f"删除用例失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '删除失败',
            'message': str(e)
        }), 500

@testplan_bp.route('/statistics', methods=['GET'])
def get_statistics():
    """
    获取用例统计信息

    Query Parameters:
        project_id: 项目ID (可选)
    """
    try:
        project_id = request.args.get('project_id')
        if project_id:
            project_id = int(project_id)

        stats = TestCaseManager.get_case_statistics(project_id)

        return jsonify({
            'success': True,
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取用例统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取统计信息失败',
            'message': str(e)
        }), 500

@testplan_bp.route('/update_status', methods=['POST'])
def update_status_from_runsim():
    """
    从RunSim GUI更新用例状态

    JSON Body:
        case_name: 用例名称
        status: 新状态
        stage_type: 阶段类型 (可选，默认'subsys')
        timestamp: 时间戳 (可选)
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': '缺少请求数据'
            }), 400

        case_name = data.get('case_name')
        status = data.get('status')
        stage_type = data.get('stage_type', 'subsys')

        if not case_name or not status:
            return jsonify({
                'success': False,
                'error': '缺少必要参数: case_name, status'
            }), 400

        # 根据用例名称查找用例ID
        # 动态导入database模块
        import importlib.util
        database_file = os.path.join(parent_dir, 'models', 'database.py')
        spec = importlib.util.spec_from_file_location("routes_database_2", database_file)
        database_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(database_module)
        get_db = getattr(database_module, 'get_db')

        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM test_cases WHERE case_name = ?", (case_name,))
            result = cursor.fetchone()

            if not result:
                return jsonify({
                    'success': False,
                    'error': f'用例不存在: {case_name}'
                }), 404

            case_id = result['id']

        # 更新状态
        success = TestCaseManager.update_case_status(case_id, stage_type, status, 'runsim_gui')

        if success:
            return jsonify({
                'success': True,
                'message': f'用例状态更新成功: {case_name}',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': '状态更新失败'
            }), 500

    except Exception as e:
        logger.error(f"从RunSim GUI更新用例状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '更新失败',
            'message': str(e)
        }), 500
