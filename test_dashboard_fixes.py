#!/usr/bin/env python3
"""
测试仪表盘修复功能的脚本

测试内容：
1. BUG趋势数据是否返回整数
2. 阶段进度统计是否正确（DVR1/DVR2/DVR3不包含POST用例）
3. 用例管理状态格式是否一致
"""

import os
import sys
import json
import requests
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_bug_trend_api():
    """测试BUG趋势API"""
    print("=" * 50)
    print("测试BUG趋势API")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # 测试按天统计
    print("\n1. 测试按天统计...")
    try:
        response = requests.get(f"{base_url}/api/bugs/trend", params={
            'days': 7,
            'unit': 'day'
        })
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                trend_data = data['data']
                print(f"✓ 按天统计成功")
                print(f"  - 标签数量: {len(trend_data.get('labels', []))}")
                print(f"  - 新增BUG数据类型: {type(trend_data.get('new_bugs', [None])[0]) if trend_data.get('new_bugs') else 'None'}")
                print(f"  - 修复BUG数据类型: {type(trend_data.get('fixed_bugs', [None])[0]) if trend_data.get('fixed_bugs') else 'None'}")
                
                # 检查是否为整数
                new_bugs = trend_data.get('new_bugs', [])
                fixed_bugs = trend_data.get('fixed_bugs', [])
                
                all_integers = all(isinstance(x, int) for x in new_bugs + fixed_bugs)
                print(f"  - 数据是否为整数: {'✓' if all_integers else '✗'}")
            else:
                print(f"✗ API返回错误: {data.get('message', '未知错误')}")
        else:
            print(f"✗ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 请求失败: {e}")
    
    # 测试按周统计
    print("\n2. 测试按周统计...")
    try:
        response = requests.get(f"{base_url}/api/bugs/trend", params={
            'days': 28,
            'unit': 'week'
        })
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                trend_data = data['data']
                print(f"✓ 按周统计成功")
                print(f"  - 统计单位: {trend_data.get('unit', 'unknown')}")
                print(f"  - 标签示例: {trend_data.get('labels', [])[:3]}")
            else:
                print(f"✗ API返回错误: {data.get('message', '未知错误')}")
        else:
            print(f"✗ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 请求失败: {e}")

def test_phase_distribution_api():
    """测试阶段分布API"""
    print("\n" + "=" * 50)
    print("测试阶段分布API")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    try:
        response = requests.get(f"{base_url}/api/dashboard/phase_distribution")
        if response.status_code == 200:
            data = response.json()
            phase_data = data.get('phase_distribution', {})
            
            print(f"✓ 阶段分布API调用成功")
            
            # 检查DVR1/DVR2/DVR3阶段是否正确排除POST用例
            for phase in ['DVR1', 'DVR2', 'DVR3']:
                if phase in phase_data:
                    phase_info = phase_data[phase]
                    post_subsys = phase_info.get('post_subsys', {}).get('total', 0)
                    post_top = phase_info.get('post_top', {}).get('total', 0)
                    
                    print(f"\n{phase}阶段:")
                    print(f"  - POST_Subsys用例数: {post_subsys}")
                    print(f"  - POST_TOP用例数: {post_top}")
                    print(f"  - POST用例是否为0: {'✓' if post_subsys == 0 and post_top == 0 else '✗'}")
            
            # 检查DVS2阶段是否包含POST用例
            if 'DVS2' in phase_data:
                dvs2_info = phase_data['DVS2']
                post_subsys = dvs2_info.get('post_subsys', {}).get('total', 0)
                post_top = dvs2_info.get('post_top', {}).get('total', 0)
                
                print(f"\nDVS2阶段:")
                print(f"  - POST_Subsys用例数: {post_subsys}")
                print(f"  - POST_TOP用例数: {post_top}")
                print(f"  - POST用例统计正常: {'✓' if post_subsys >= 0 and post_top >= 0 else '✗'}")
                
        else:
            print(f"✗ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 请求失败: {e}")

def test_testplan_statistics_api():
    """测试用例管理统计API"""
    print("\n" + "=" * 50)
    print("测试用例管理统计API")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    try:
        response = requests.get(f"{base_url}/api/testplan/statistics")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data['data']
                print(f"✓ 用例管理统计API调用成功")
                
                # 检查状态格式
                status_fields = ['total_passed', 'total_pending', 'total_ongoing', 'total_failed']
                print(f"\n状态统计:")
                for field in status_fields:
                    value = stats.get(field, 0)
                    print(f"  - {field}: {value}")
                
                # 检查级别统计
                level_fields = ['subsys_pass', 'subsys_pending', 'subsys_ongoing', 'subsys_fail',
                               'top_pass', 'top_pending', 'top_ongoing', 'top_fail']
                print(f"\n级别统计:")
                for field in level_fields:
                    value = stats.get(field, 0)
                    print(f"  - {field}: {value}")
                    
            else:
                print(f"✗ API返回错误: {data.get('error', '未知错误')}")
        else:
            print(f"✗ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 请求失败: {e}")

def main():
    """主函数"""
    print("RunSim 仪表盘修复功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试BUG趋势API
    test_bug_trend_api()
    
    # 测试阶段分布API
    test_phase_distribution_api()
    
    # 测试用例管理统计API
    test_testplan_statistics_api()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    print("\n请检查以上输出，确认修复是否正确。")
    print("如果看到 ✗ 标记，说明该项目需要进一步修复。")

if __name__ == "__main__":
    main()
