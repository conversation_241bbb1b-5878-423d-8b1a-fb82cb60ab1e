#!/usr/bin/env python3
"""
BUG创建功能测试脚本

该脚本专门测试BUG创建功能，帮助诊断创建失败的问题
"""

import os
import sys
import json
from datetime import datetime, date

# 添加路径
sys.path.insert(0, '.')

def test_bug_model_creation():
    """测试BUG模型创建功能"""
    print("=== 测试BUG模型创建功能 ===")
    
    try:
        from models.bug import BugModel
        
        db_path = 'data/dashboard.db'
        bug_model = BugModel(db_path)
        
        # 测试数据
        test_cases = [
            {
                'name': '最小必填字段',
                'data': {
                    'bug_id': 'TEST-MIN-001',
                    'description': '最小测试BUG'
                }
            },
            {
                'name': '完整字段',
                'data': {
                    'bug_id': 'TEST-FULL-001',
                    'bug_type': '功能缺陷',
                    'description': '完整测试BUG',
                    'severity': 'High',
                    'status': 'Open',
                    'submitter': '测试用户',
                    'submit_date': date.today().isoformat()
                }
            },
            {
                'name': '空字符串字段',
                'data': {
                    'bug_id': 'TEST-EMPTY-001',
                    'description': '空字符串测试BUG',
                    'bug_type': '',
                    'submitter': '',
                    'submit_date': ''
                }
            }
        ]
        
        for test_case in test_cases:
            print(f"\n测试用例: {test_case['name']}")
            print(f"数据: {test_case['data']}")
            
            # 先删除可能存在的同名BUG
            existing_bug = bug_model.get_bug_by_bug_id(test_case['data']['bug_id'])
            if existing_bug:
                bug_model.delete_bug(existing_bug['id'])
                print(f"删除已存在的BUG: {test_case['data']['bug_id']}")
            
            # 创建BUG
            bug_id = bug_model.create_bug(test_case['data'])
            if bug_id:
                print(f"✓ 创建成功，ID: {bug_id}")
                
                # 验证创建的BUG
                created_bug = bug_model.get_bug_by_id(bug_id)
                if created_bug:
                    print(f"✓ 验证成功，BUG ID: {created_bug['bug_id']}")
                else:
                    print("✗ 验证失败，无法获取创建的BUG")
            else:
                print("✗ 创建失败")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_creation():
    """测试API创建功能"""
    print("\n=== 测试API创建功能 ===")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试数据
            test_cases = [
                {
                    'name': '最小必填字段',
                    'data': {
                        'bug_id': 'API-MIN-001',
                        'description': 'API最小测试BUG'
                    }
                },
                {
                    'name': '完整字段',
                    'data': {
                        'bug_id': 'API-FULL-001',
                        'bug_type': '性能问题',
                        'description': 'API完整测试BUG',
                        'severity': 'Medium',
                        'status': 'Open',
                        'submitter': 'API测试用户',
                        'submit_date': date.today().isoformat()
                    }
                },
                {
                    'name': '空字符串字段',
                    'data': {
                        'bug_id': 'API-EMPTY-001',
                        'description': 'API空字符串测试BUG',
                        'bug_type': '',
                        'submitter': '',
                        'submit_date': ''
                    }
                }
            ]
            
            for test_case in test_cases:
                print(f"\n测试用例: {test_case['name']}")
                print(f"数据: {test_case['data']}")
                
                # 发送POST请求
                response = client.post('/api/bugs',
                                     data=json.dumps(test_case['data']),
                                     content_type='application/json')
                
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.get_json()
                    print(f"响应数据: {data}")
                    
                    if data.get('success'):
                        print(f"✓ API创建成功，ID: {data['data']['id']}")
                    else:
                        print(f"✗ API创建失败: {data.get('message')}")
                else:
                    print(f"✗ API请求失败: {response.status_code}")
                    try:
                        error_data = response.get_json()
                        print(f"错误信息: {error_data}")
                    except:
                        print(f"响应文本: {response.get_data(as_text=True)}")
        
        return True
        
    except Exception as e:
        print(f"✗ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_structure():
    """测试数据库结构"""
    print("\n=== 测试数据库结构 ===")
    
    try:
        import sqlite3
        
        db_path = 'data/dashboard.db'
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查bugs表结构
            cursor.execute("PRAGMA table_info(bugs)")
            columns = cursor.fetchall()
            
            print("bugs表结构:")
            for col in columns:
                print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
            
            # 检查projects表是否有数据
            cursor.execute("SELECT COUNT(*) FROM projects")
            project_count = cursor.fetchone()[0]
            print(f"\nprojects表记录数: {project_count}")
            
            if project_count == 0:
                print("警告: projects表为空，这可能导致外键约束问题")
                # 插入默认项目
                cursor.execute('''
                    INSERT INTO projects (name, subsystem, description)
                    VALUES (?, ?, ?)
                ''', ('默认项目', 'default', '系统默认项目'))
                conn.commit()
                print("已插入默认项目")
            
            # 检查现有BUG数量
            cursor.execute("SELECT COUNT(*) FROM bugs")
            bug_count = cursor.fetchone()[0]
            print(f"bugs表记录数: {bug_count}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("BUG创建功能诊断测试")
    print("=" * 50)
    
    tests = [
        test_database_structure,
        test_bug_model_creation,
        test_api_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"\n测试失败，继续下一个测试...")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！BUG创建功能正常。")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
    
    print("\n建议:")
    print("1. 检查浏览器开发者工具的Console和Network标签页")
    print("2. 查看Flask应用的日志输出")
    print("3. 确认数据库文件权限正常")
    print("4. 验证表单字段名称与API期望的字段名称一致")

if __name__ == '__main__':
    main()
