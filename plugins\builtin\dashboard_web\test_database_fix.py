#!/usr/bin/env python
"""
测试数据库修复效果
"""

import os
import sys
import requests
import sqlite3

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_database_fix():
    """测试数据库修复效果"""
    print("🧪 测试数据库修复效果")
    print("=" * 40)
    
    # 1. 检查数据库路径
    print("1. 检查数据库路径...")
    
    # 模拟app.py中的路径计算
    dashboard_web_dir = os.path.dirname(os.path.abspath(__file__))
    database_path = os.path.join(dashboard_web_dir, 'data', 'dashboard.db')
    
    print(f"   📁 计算的数据库路径: {database_path}")
    print(f"   📁 文件存在: {os.path.exists(database_path)}")
    
    if not os.path.exists(database_path):
        print("   ❌ 数据库文件不存在")
        return False
    
    # 2. 检查数据库内容
    print("\n2. 检查数据库内容...")
    
    try:
        with sqlite3.connect(database_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) as count FROM test_cases")
            case_count = cursor.fetchone()['count']
            print(f"   📊 用例数量: {case_count}")
            
            if case_count > 0:
                cursor.execute("SELECT case_name FROM test_cases LIMIT 3")
                cases = cursor.fetchall()
                print("   📋 示例用例:")
                for case in cases:
                    print(f"      {case['case_name']}")
            else:
                print("   ⚠️ 数据库中没有用例")
                
    except Exception as e:
        print(f"   ❌ 检查数据库失败: {e}")
        return False
    
    # 3. 测试API
    print("\n3. 测试API...")
    
    base_url = "http://127.0.0.1:5001"
    
    try:
        # 健康检查
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ 健康检查API正常")
        else:
            print(f"   ❌ 健康检查API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 无法连接API: {e}")
        return False
    
    try:
        # 用例列表API
        response = requests.get(f"{base_url}/api/testplan/cases", timeout=10)
        print(f"   📡 用例列表API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                cases = data.get('data', {}).get('cases', [])
                print(f"   ✅ 用例列表API正常，返回 {len(cases)} 个用例")
                
                if len(cases) > 0:
                    print("   📋 API返回的示例用例:")
                    for case in cases[:3]:
                        print(f"      {case.get('case_name', 'N/A')}")
                else:
                    print("   ⚠️ API返回空用例列表")
            else:
                print(f"   ❌ 用例列表API返回错误: {data.get('error')}")
                return False
        else:
            print(f"   ❌ 用例列表API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 用例列表API异常: {e}")
        return False
    
    # 4. 测试导入API（如果有模板文件）
    print("\n4. 测试导入API...")
    
    template_path = os.path.join(current_dir, '..', '..', '..', 'TestPlan_Template.xlsx')
    template_path = os.path.abspath(template_path)
    
    if os.path.exists(template_path):
        print(f"   📁 找到模板文件: {os.path.basename(template_path)}")
        
        try:
            with open(template_path, 'rb') as f:
                files = {
                    'file': ('TestPlan_Template.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                }
                data = {
                    'project_id': '1',
                    'sheet_name': 'TP'
                }
                
                response = requests.post(
                    f"{base_url}/api/testplan/import",
                    files=files,
                    data=data,
                    timeout=30
                )
                
                print(f"   📡 导入API状态: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print("   ✅ 导入API正常")
                        data = result.get('data', {})
                        print(f"      解析: {data.get('total_parsed', 0)} 条")
                        print(f"      导入: {data.get('imported_count', 0)} 条")
                    else:
                        print(f"   ⚠️ 导入失败: {result.get('error')}")
                else:
                    print(f"   ❌ 导入API失败: {response.status_code}")
                    
        except Exception as e:
            print(f"   ❌ 导入API测试异常: {e}")
    else:
        print("   ⚠️ 未找到TestPlan模板文件，跳过导入测试")
    
    print("\n🎉 数据库修复测试完成!")
    return True

if __name__ == '__main__':
    success = test_database_fix()
    if success:
        print("\n✅ 修复验证通过!")
        print("现在独立启动和RunSim GUI启动应该使用相同的数据库")
    else:
        print("\n❌ 修复验证失败!")
        print("需要进一步检查问题")
