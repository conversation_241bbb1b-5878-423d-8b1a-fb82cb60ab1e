#!/usr/bin/env python3
"""
第一阶段验证脚本

简单验证第一阶段基础框架搭建的完成情况
"""

import os
import sys
import sqlite3
from datetime import datetime

def check_file_structure():
    """检查文件结构"""
    print("检查文件结构...")
    
    required_files = [
        'dashboard_plugin.py',
        'dashboard_web/__init__.py',
        'dashboard_web/app.py',
        'dashboard_web/models/database.py',
        'dashboard_web/routes/api.py',
        'dashboard_web/templates/base.html',
        'dashboard_web/templates/dashboard.html',
        'dashboard_web/templates/error.html',
        'dashboard_web/templates/testplan.html',
        'dashboard_web/templates/bug.html',
        'dashboard_web/utils/test_data.py',
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"  ✓ {file_path}")
    
    if missing_files:
        print(f"  ✗ 缺少文件: {missing_files}")
        return False
    
    print("  ✓ 所有必要文件都存在")
    return True

def test_database_module():
    """测试数据库模块"""
    print("\n测试数据库模块...")
    
    try:
        # 切换到dashboard_web目录
        original_cwd = os.getcwd()
        dashboard_path = os.path.join(os.getcwd(), 'dashboard_web')
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)
        
        try:
            from models.database import init_database, DatabaseManager
            
            # 测试数据库初始化
            test_db_path = os.path.join('data', 'test_verify.db')
            os.makedirs(os.path.dirname(test_db_path), exist_ok=True)
            
            result = init_database(test_db_path)
            if result:
                print("  ✓ 数据库初始化成功")
                
                # 检查表是否创建
                db_manager = DatabaseManager(test_db_path)
                with db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                
                expected_tables = ['projects', 'test_cases', 'bugs', 'case_status_history', 'system_config']
                missing_tables = [t for t in expected_tables if t not in tables]
                
                if missing_tables:
                    print(f"  ✗ 缺少表: {missing_tables}")
                    return False
                else:
                    print(f"  ✓ 所有表创建成功: {tables}")
                    return True
            else:
                print("  ✗ 数据库初始化失败")
                return False
                
        finally:
            os.chdir(original_cwd)
            
    except Exception as e:
        print(f"  ✗ 数据库模块测试失败: {str(e)}")
        return False

def test_flask_app():
    """测试Flask应用"""
    print("\n测试Flask应用...")
    
    try:
        # 检查Flask是否可用
        try:
            import flask
            print(f"  ✓ Flask可用，版本: {flask.__version__}")
        except ImportError:
            print("  ✗ Flask未安装")
            return False
        
        # 切换到dashboard_web目录
        original_cwd = os.getcwd()
        dashboard_path = os.path.join(os.getcwd(), 'dashboard_web')
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)
        
        try:
            from app import create_app
            
            # 创建测试应用
            test_config = {
                'DATABASE_PATH': os.path.join('data', 'test_app.db'),
                'TESTING': True
            }
            
            app = create_app(test_config)
            print("  ✓ Flask应用创建成功")
            
            # 测试应用上下文
            with app.app_context():
                print("  ✓ 应用上下文正常")
            
            return True
            
        finally:
            os.chdir(original_cwd)
            
    except Exception as e:
        print(f"  ✗ Flask应用测试失败: {str(e)}")
        return False

def test_plugin_import():
    """测试插件导入"""
    print("\n测试插件导入...")
    
    try:
        from dashboard_plugin import DashboardPlugin
        
        plugin = DashboardPlugin()
        print(f"  ✓ 插件导入成功")
        print(f"  ✓ 插件名称: {plugin.name}")
        print(f"  ✓ 插件版本: {plugin.version}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ 插件导入失败: {str(e)}")
        return False

def generate_sample_data():
    """生成示例数据"""
    print("\n生成示例数据...")
    
    try:
        # 切换到dashboard_web目录
        original_cwd = os.getcwd()
        dashboard_path = os.path.join(os.getcwd(), 'dashboard_web')
        os.chdir(dashboard_path)
        sys.path.insert(0, dashboard_path)
        
        try:
            from utils.test_data import generate_test_data
            
            db_path = os.path.join('data', 'dashboard.db')
            generate_test_data(db_path)
            print("  ✓ 示例数据生成成功")
            
            return True
            
        finally:
            os.chdir(original_cwd)
            
    except Exception as e:
        print(f"  ✗ 示例数据生成失败: {str(e)}")
        return False

def main():
    """主验证函数"""
    print("=" * 60)
    print("RunSim Dashboard 第一阶段验证")
    print("=" * 60)
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 验证步骤
    tests = [
        ("文件结构检查", check_file_structure),
        ("数据库模块测试", test_database_module),
        ("Flask应用测试", test_flask_app),
        ("插件导入测试", test_plugin_import),
        ("生成示例数据", generate_sample_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ✗ {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 60)
    print("验证结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个验证通过")
    
    if passed == total:
        print("\n🎉 第一阶段基础框架搭建完成！")
        print("\n✅ 已完成的功能:")
        print("   - 插件系统集成")
        print("   - Flask Web服务器框架")
        print("   - SQLite数据库设计")
        print("   - HTML模板系统")
        print("   - API接口框架")
        print("   - 示例数据生成")
        
        print("\n🚀 启动说明:")
        print("   1. 确保已安装Flask: pip install flask openpyxl")
        print("   2. 在RunSim GUI中启用仪表板插件")
        print("   3. 点击工具菜单中的'项目仪表板'")
        print("   4. 浏览器将自动打开仪表板页面")
        
        print("\n⏭️  下一步: 开始第二阶段 - Excel解析和用例管理")
        return True
    else:
        print("\n⚠️  验证未完全通过，请检查失败的项目")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
