import openpyxl
import os

if os.path.exists("TestPlan_Template.xlsx"):
    try:
        wb = openpyxl.load_workbook("TestPlan_Template.xlsx")
        ws = wb["TP"]
        
        print("✅ 文件生成成功")
        print(f"行数: {ws.max_row}, 列数: {ws.max_column}")
        
        # 检查修改后的表头
        print("\n📋 更新后的表头:")
        print(f"C列: {ws['C3'].value}")  # Test Areas
        print(f"E列: {ws['E3'].value}")  # Test Scope  
        print(f"G列: {ws['G3'].value}")  # Cover
        print(f"H列: {ws['H3'].value}")  # TestCase Name
        
        wb.close()
        print("\n🎉 表头修改完成!")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
else:
    print("❌ 文件不存在")
